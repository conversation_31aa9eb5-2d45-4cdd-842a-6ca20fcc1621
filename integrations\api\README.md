# Unified Messaging API Documentation

## Overview

The Unified Messaging API provides a centralized interface for sending messages across multiple social media platforms using Unipile API as the primary backend with platform-specific fallbacks.

## Supported Platforms

- **WhatsApp** - Business messaging with QR code authentication
- **Telegram** - Bot messaging with token authentication  
- **Facebook Messenger** - Page messaging with OAuth
- **Instagram** - Direct messaging with OAuth
- **LinkedIn** - Professional messaging with OAuth and InMail support
- **TikTok** - Comment interactions with OAuth

## API Endpoints

### Base URL
- **Flask**: `http://localhost:5000/api`
- **FastAPI**: `http://localhost:8000/api` (with automatic OpenAPI docs at `/docs`)

### Core Messaging Endpoints

#### 1. Send Single Message
```http
POST /api/messaging/send
Content-Type: application/json

{
  "platform": "whatsapp",
  "recipient": "+*********0",
  "message": "Hello from unified messaging!",
  "options": {}
}
```

**Response:**
```json
{
  "success": true,
  "platform": "whatsapp",
  "recipient": "+*********0",
  "message_id": "msg_123456",
  "method": "unipile",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 2. Send Bulk Messages
```http
POST /api/messaging/bulk
Content-Type: application/json

{
  "campaign": {
    "whatsapp": [
      {"contact": "+*********0", "message": "Hello WhatsApp!"},
      {"contact": "+0987654321", "message": "Hi there!"}
    ],
    "telegram": [
      {"contact": "@username", "message": "Hello Telegram!"}
    ]
  },
  "delay": 2.0
}
```

**Response:**
```json
{
  "success": true,
  "campaign_summary": {
    "total_messages": 3,
    "successful": 3,
    "failed": 0,
    "success_rate": 100.0
  },
  "results": {
    "whatsapp": [
      {
        "success": true,
        "recipient": "+*********0",
        "message_id": "msg_123",
        "method": "unipile",
        "timestamp": "2024-01-01T12:00:00Z"
      }
    ]
  }
}
```

#### 3. Send Template Message
```http
POST /api/messaging/template
Content-Type: application/json

{
  "template_name": "welcome",
  "recipients": [
    {
      "platform": "whatsapp",
      "contact": "+*********0",
      "name": "John Doe"
    }
  ],
  "variables": {
    "company": "ACME Corp"
  }
}
```

### Status and Analytics Endpoints

#### 4. Get Platform Status
```http
GET /api/messaging/status/whatsapp
GET /api/messaging/status/all
```

**Response:**
```json
{
  "success": true,
  "platform": "whatsapp",
  "status": {
    "configured": true,
    "connected": true,
    "unipile": {
      "available": true,
      "connected": true
    },
    "platform_api": {
      "available": true,
      "connected": false
    }
  }
}
```

#### 5. Get Message Analytics
```http
GET /api/messaging/analytics
```

**Response:**
```json
{
  "success": true,
  "analytics": {
    "total_messages": 150,
    "successful_messages": 145,
    "failed_messages": 5,
    "success_rate": 96.67,
    "platform_breakdown": {
      "whatsapp": {"total": 50, "successful": 50},
      "telegram": {"total": 30, "successful": 28}
    }
  }
}
```

### Configuration Endpoints

#### 6. Update Platform Configuration
```http
POST /api/whatsapp/config
Content-Type: application/json

{
  "phone_number_id": "*********",
  "access_token": "your_access_token",
  "webhook_verify_token": "your_verify_token"
}
```

#### 7. Test Platform Connection
```http
GET /api/whatsapp/test
```

#### 8. Connect Unipile
```http
POST /api/unipile/connect
Content-Type: application/json

{
  "api_key": "your_unipile_api_key"
}
```

#### 9. Get Unipile Accounts
```http
GET /api/unipile/accounts
```

### Utility Endpoints

#### 10. Health Check
```http
GET /api/health
```

#### 11. Get Available Templates
```http
GET /api/config/templates
```

#### 12. Get Supported Platforms
```http
GET /api/config/platforms
```

## Usage Examples

### Python Client Example

```python
import requests
import json

# Base URL
BASE_URL = "http://localhost:5000/api"

# Send single message
def send_message(platform, recipient, message):
    response = requests.post(f"{BASE_URL}/messaging/send", json={
        "platform": platform,
        "recipient": recipient,
        "message": message
    })
    return response.json()

# Send bulk campaign
def send_bulk_campaign(campaign_data):
    response = requests.post(f"{BASE_URL}/messaging/bulk", json={
        "campaign": campaign_data,
        "delay": 2.0
    })
    return response.json()

# Check platform status
def check_status(platform="all"):
    response = requests.get(f"{BASE_URL}/messaging/status/{platform}")
    return response.json()

# Example usage
if __name__ == "__main__":
    # Send single message
    result = send_message("whatsapp", "+*********0", "Hello!")
    print("Single message result:", result)
    
    # Send bulk campaign
    campaign = {
        "whatsapp": [
            {"contact": "+*********0", "message": "Hello WhatsApp!"}
        ],
        "telegram": [
            {"contact": "@username", "message": "Hello Telegram!"}
        ]
    }
    bulk_result = send_bulk_campaign(campaign)
    print("Bulk campaign result:", bulk_result)
    
    # Check status
    status = check_status()
    print("Platform status:", status)
```

### JavaScript/Node.js Example

```javascript
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Send single message
async function sendMessage(platform, recipient, message) {
    try {
        const response = await axios.post(`${BASE_URL}/messaging/send`, {
            platform,
            recipient,
            message
        });
        return response.data;
    } catch (error) {
        console.error('Error sending message:', error.response?.data || error.message);
        throw error;
    }
}

// Send bulk campaign
async function sendBulkCampaign(campaignData) {
    try {
        const response = await axios.post(`${BASE_URL}/messaging/bulk`, {
            campaign: campaignData,
            delay: 2.0
        });
        return response.data;
    } catch (error) {
        console.error('Error sending bulk campaign:', error.response?.data || error.message);
        throw error;
    }
}

// Check platform status
async function checkStatus(platform = 'all') {
    try {
        const response = await axios.get(`${BASE_URL}/messaging/status/${platform}`);
        return response.data;
    } catch (error) {
        console.error('Error checking status:', error.response?.data || error.message);
        throw error;
    }
}

// Example usage
async function main() {
    try {
        // Send single message
        const result = await sendMessage('whatsapp', '+*********0', 'Hello!');
        console.log('Single message result:', result);
        
        // Send bulk campaign
        const campaign = {
            whatsapp: [
                { contact: '+*********0', message: 'Hello WhatsApp!' }
            ],
            telegram: [
                { contact: '@username', message: 'Hello Telegram!' }
            ]
        };
        const bulkResult = await sendBulkCampaign(campaign);
        console.log('Bulk campaign result:', bulkResult);
        
        // Check status
        const status = await checkStatus();
        console.log('Platform status:', status);
        
    } catch (error) {
        console.error('Error in main:', error);
    }
}

main();
```

### cURL Examples

```bash
# Send single message
curl -X POST http://localhost:5000/api/messaging/send \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "whatsapp",
    "recipient": "+*********0",
    "message": "Hello from cURL!"
  }'

# Send bulk messages
curl -X POST http://localhost:5000/api/messaging/bulk \
  -H "Content-Type: application/json" \
  -d '{
    "campaign": {
      "whatsapp": [
        {"contact": "+*********0", "message": "Hello!"}
      ]
    },
    "delay": 2.0
  }'

# Check platform status
curl http://localhost:5000/api/messaging/status/all

# Connect Unipile
curl -X POST http://localhost:5000/api/unipile/connect \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "your_unipile_api_key"
  }'
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error description",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (invalid input)
- `404` - Not Found (platform/template not found)
- `500` - Internal Server Error
- `503` - Service Unavailable (system not initialized)

## Rate Limiting

The API implements rate limiting based on platform requirements:
- **WhatsApp**: 1 message per second
- **Telegram**: 2 messages per second
- **Facebook**: 1 message per 2 seconds
- **Instagram**: 1 message per 3 seconds
- **LinkedIn**: 1 message per 3 seconds
- **TikTok**: 1 message per 2 seconds

## Authentication

Currently, the API uses platform-specific authentication:
- Configure each platform individually via `/api/{platform}/config`
- Set Unipile API key via `/api/unipile/connect`
- Future versions will support API key authentication for the unified API

## Getting Started

1. **Install dependencies:**
   ```bash
   python integrations/setup_unified_messaging.py
   ```

2. **Configure Unipile:**
   ```bash
   export UNIPILE_API_KEY='your_api_key'
   ```

3. **Start the API server:**
   ```bash
   # Flask
   python integrations/api/messaging_endpoints.py
   
   # FastAPI
   python integrations/api/fastapi_endpoints.py
   ```

4. **Test the API:**
   ```bash
   curl http://localhost:5000/api/health
   ```

5. **Configure platforms and start messaging!**
