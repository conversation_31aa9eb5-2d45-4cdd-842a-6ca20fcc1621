#!/usr/bin/env python3
"""
Unified Messaging System Setup Script
Installs dependencies and configures the unified messaging system
"""

import os
import sys
import subprocess
import json
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedMessagingSetup:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.requirements = [
            "unipile-python-sdk",
            "requests",
            "python-telegram-bot",
            "facebook-sdk",
            "linkedin-api",
            "tiktok-api",
            "qrcode[pil]",
            "Pillow",
            "asyncio",
            "aiohttp"
        ]
        
    def install_dependencies(self):
        """Install required Python packages"""
        logger.info("🚀 Installing Unified Messaging System dependencies...")
        
        for package in self.requirements:
            try:
                logger.info(f"📦 Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                logger.info(f"✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ Failed to install {package}: {e}")
                # Continue with other packages
                continue
        
        logger.info("✅ Dependency installation completed")
    
    def create_directory_structure(self):
        """Create necessary directory structure"""
        logger.info("📁 Creating directory structure...")
        
        directories = [
            "templates",
            "logs",
            "exports",
            "webhooks",
            "static/css",
            "static/js",
            "static/images"
        ]
        
        for directory in directories:
            dir_path = self.base_dir / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"📂 Created directory: {directory}")
        
        logger.info("✅ Directory structure created")
    
    def setup_configuration_files(self):
        """Setup configuration files if they don't exist"""
        logger.info("⚙️ Setting up configuration files...")
        
        # Check if unipile_config.json exists
        unipile_config_path = self.base_dir / "unipile_config.json"
        if not unipile_config_path.exists():
            logger.info("📝 Creating unipile_config.json...")
            default_unipile_config = {
                "api_key": "",
                "base_url": "https://api1.unipile.com:13115/api/v1",
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 1.0,
                "supported_platforms": [
                    "whatsapp", "telegram", "facebook", "instagram", "linkedin", "tiktok"
                ],
                "rate_limits": {
                    "messages_per_minute": 60,
                    "messages_per_hour": 1000,
                    "bulk_message_delay": 2.0
                }
            }
            
            with open(unipile_config_path, 'w') as f:
                json.dump(default_unipile_config, f, indent=2)
            logger.info("✅ unipile_config.json created")
        
        # Check if unified_messaging_config.json exists
        unified_config_path = self.base_dir / "unified_messaging_config.json"
        if unified_config_path.exists():
            logger.info("✅ unified_messaging_config.json already exists")
        else:
            logger.warning("⚠️ unified_messaging_config.json not found - should be created by save-file")
        
        logger.info("✅ Configuration files setup completed")
    
    def test_unipile_installation(self):
        """Test if Unipile SDK is properly installed"""
        logger.info("🧪 Testing Unipile SDK installation...")
        
        try:
            import unipile
            logger.info("✅ Unipile SDK imported successfully")
            
            # Try to create a client (will fail without API key, but tests import)
            try:
                from unipile import UnipileClient
                logger.info("✅ UnipileClient class available")
            except ImportError as e:
                logger.error(f"❌ UnipileClient import failed: {e}")
                return False
                
        except ImportError as e:
            logger.error(f"❌ Unipile SDK not properly installed: {e}")
            logger.info("💡 Try installing manually: pip install unipile-python-sdk")
            return False
        
        return True
    
    def create_example_scripts(self):
        """Create example usage scripts"""
        logger.info("📝 Creating example scripts...")
        
        # Example usage script
        example_script = '''#!/usr/bin/env python3
"""
Example usage of the Unified Messaging System
"""

from unified_messaging import UnifiedMessaging, MessageRecipient
from unipile_config import get_unipile_client, is_unipile_available

def main():
    print("🚀 Unified Messaging System Example")
    
    # Initialize unified messaging
    um = UnifiedMessaging()
    
    # Check platform status
    print("\\n📊 Platform Status:")
    status = um.get_platform_status()
    for platform, info in status.items():
        configured = info.get('configured', False)
        available = info.get('available', False)
        status_icon = "✅" if configured and available else "❌"
        print(f"  {status_icon} {platform}: {'Ready' if configured and available else 'Not Ready'}")
    
    # Check Unipile availability
    print(f"\\n🔗 Unipile Available: {'✅ Yes' if is_unipile_available() else '❌ No'}")
    
    # Example single message (uncomment and modify to test)
    # result = um.send_message_by_platform('whatsapp', '+1234567890', 'Hello from unified messaging!')
    # print(f"\\n📤 Message result: {result}")
    
    # Example bulk campaign (uncomment and modify to test)
    # campaign = {
    #     'whatsapp': [
    #         {'contact': '+1234567890', 'message': 'Hello WhatsApp!'},
    #         {'contact': '+0987654321', 'message': 'Hi there!'}
    #     ],
    #     'telegram': [
    #         {'contact': '@username', 'message': 'Hello Telegram!'}
    #     ]
    # }
    # results = um.send_bulk_campaign(campaign)
    # print(f"\\n📊 Campaign results: {results}")
    
    # Get analytics
    analytics = um.get_message_analytics()
    print(f"\\n📈 Analytics: {analytics}")

if __name__ == "__main__":
    main()
'''
        
        example_path = self.base_dir / "example_usage.py"
        with open(example_path, 'w') as f:
            f.write(example_script)
        
        # Make it executable
        os.chmod(example_path, 0o755)
        logger.info("✅ example_usage.py created")
        
        # Quick test script
        test_script = '''#!/usr/bin/env python3
"""
Quick test script for Unified Messaging System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unipile_config import UnipileConfig, is_unipile_available
from unified_messaging import UnifiedMessaging

def main():
    print("🧪 Unified Messaging System - Quick Test")
    print("=" * 50)
    
    # Test Unipile configuration
    print("\\n1. Testing Unipile Configuration:")
    config = UnipileConfig()
    if config.is_configured():
        print("✅ Unipile API key configured")
        test_result = config.test_connection()
        if test_result.get("success"):
            print(f"✅ Connection successful: {test_result['message']}")
        else:
            print(f"❌ Connection failed: {test_result['error']}")
    else:
        print("❌ Unipile API key not configured")
        print("💡 Set UNIPILE_API_KEY environment variable or update unipile_config.json")
    
    # Test unified messaging initialization
    print("\\n2. Testing Unified Messaging Initialization:")
    try:
        um = UnifiedMessaging()
        print("✅ Unified messaging initialized successfully")
        
        # Get platform status
        status = um.get_platform_status()
        print(f"✅ Platform status retrieved: {len(status)} platforms")
        
        for platform, info in status.items():
            configured = info.get('configured', False)
            available = info.get('available', False)
            status_icon = "✅" if configured and available else "❌"
            print(f"  {status_icon} {platform}")
            
    except Exception as e:
        print(f"❌ Unified messaging initialization failed: {e}")
    
    print("\\n" + "=" * 50)
    print("🎉 Quick test completed!")
    print("💡 Run 'python example_usage.py' for more examples")

if __name__ == "__main__":
    main()
'''
        
        test_path = self.base_dir / "quick_test.py"
        with open(test_path, 'w') as f:
            f.write(test_script)
        
        # Make it executable
        os.chmod(test_path, 0o755)
        logger.info("✅ quick_test.py created")
    
    def display_setup_summary(self):
        """Display setup summary and next steps"""
        logger.info("🎉 Unified Messaging System Setup Complete!")
        print("\n" + "=" * 60)
        print("🚀 UNIFIED MESSAGING SYSTEM SETUP COMPLETE")
        print("=" * 60)
        
        print("\n📋 What was installed:")
        print("  ✅ Python dependencies (unipile-python-sdk, etc.)")
        print("  ✅ Directory structure")
        print("  ✅ Configuration files")
        print("  ✅ Example scripts")
        print("  ✅ Base authentication template")
        
        print("\n⚙️ Next Steps:")
        print("  1. Set your Unipile API key:")
        print("     - Environment variable: export UNIPILE_API_KEY='your_key_here'")
        print("     - Or edit: integrations/unipile_config.json")
        
        print("\n  2. Configure individual platforms:")
        print("     - WhatsApp: integrations/whatsapp_integration/whatsapp_auth.html")
        print("     - Telegram: integrations/telegram_integration/telegram_auth.html")
        print("     - Facebook: integrations/facebook_integration/facebook_auth.html")
        print("     - Instagram: integrations/instagram_integration/instagram_auth.html")
        print("     - LinkedIn: integrations/linkedin_integration/linkedin_auth.html")
        print("     - TikTok: integrations/tiktok_integration/tiktok_auth.html")
        
        print("\n  3. Test the system:")
        print("     - Run: python integrations/quick_test.py")
        print("     - Run: python integrations/example_usage.py")
        
        print("\n  4. Start using unified messaging:")
        print("     - Import: from integrations.unified_messaging import UnifiedMessaging")
        print("     - Initialize: um = UnifiedMessaging()")
        print("     - Send message: um.send_message_by_platform('whatsapp', '+1234567890', 'Hello!')")
        
        print("\n📚 Documentation:")
        print("  - Unipile API: https://docs.unipile.com")
        print("  - Platform-specific guides in each integration folder")
        
        print("\n" + "=" * 60)
    
    def run_setup(self):
        """Run the complete setup process"""
        logger.info("🚀 Starting Unified Messaging System Setup...")
        
        try:
            self.install_dependencies()
            self.create_directory_structure()
            self.setup_configuration_files()
            self.test_unipile_installation()
            self.create_example_scripts()
            self.display_setup_summary()
            
            logger.info("✅ Setup completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False

def main():
    """Main setup function"""
    setup = UnifiedMessagingSetup()
    success = setup.run_setup()
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("💡 Run 'python quick_test.py' to test your installation")
    else:
        print("\n❌ Setup failed. Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
