"""
WhatsApp Messaging API Integration
Handles authentication and messaging for WhatsApp Business accounts
Uses Unipile API as primary method with WhatsApp Business API fallback
"""

import requests
import json
import time
import qrcode
import io
import base64
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_config import UnipileConfig, get_unipile_client, is_unipile_available

class WhatsAppMessaging:
    def __init__(self, config_path: str = "integrations/whatsapp_integration/config.json", use_unipile: bool = True):
        """Initialize WhatsApp messaging client with Unipile and WhatsApp Business API support"""
        self.config_path = config_path
        self.config = self._load_config()
        self.use_unipile = use_unipile
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Unipile API setup (primary method)
        self.unipile_client = None
        if use_unipile:
            try:
                self.unipile_client = get_unipile_client()
                self.logger.info("Unipile API client initialized for WhatsApp")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Unipile API: {e}")
                self.use_unipile = False
        
        # WhatsApp Business API setup (fallback)
        self.phone_number_id = self.config.get("phone_number_id")
        self.access_token = self.config.get("access_token")
        self.business_account_id = self.config.get("business_account_id")
        self.webhook_verify_token = self.config.get("webhook_verify_token")
        self.api_version = self.config.get("api_version", "v18.0")
        self.base_url = f"https://graph.facebook.com/{self.api_version}"
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 second between requests
        
        # Authentication status
        self.connection_status = {
            "unipile": False,
            "whatsapp_business": False,
            "qr_code": None,
            "last_check": None
        }
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file: {e}")
            return {}
    
    def _save_config(self):
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            self.logger.info("WhatsApp configuration saved")
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def authenticate_account(self, account_id: str = None) -> Dict:
        """
        Authenticate WhatsApp account via Unipile
        Generates QR code for WhatsApp Web authentication
        """
        if not self.unipile_client:
            return {"error": "Unipile client not available"}
        
        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("accounts", []) 
                               if acc.get("provider") == "whatsapp"]
            
            if whatsapp_accounts:
                self.connection_status["unipile"] = True
                self.connection_status["last_check"] = datetime.now().isoformat()
                self.logger.info("WhatsApp account already connected via Unipile")
                return {
                    "success": True,
                    "message": "WhatsApp account connected",
                    "accounts": whatsapp_accounts,
                    "qr_required": False
                }
            
            # Generate QR code for authentication
            qr_response = self.unipile_client.generate_whatsapp_qr()
            if "qr_code" in qr_response:
                # Generate QR code image
                qr_code_data = qr_response["qr_code"]
                qr_img = qrcode.make(qr_code_data)
                
                # Convert to base64 for HTML display
                img_buffer = io.BytesIO()
                qr_img.save(img_buffer, format='PNG')
                img_buffer.seek(0)
                qr_base64 = base64.b64encode(img_buffer.getvalue()).decode()
                
                self.connection_status["qr_code"] = f"data:image/png;base64,{qr_base64}"
                self.connection_status["last_check"] = datetime.now().isoformat()
                
                return {
                    "success": True,
                    "message": "QR code generated. Please scan with WhatsApp mobile app.",
                    "qr_code": self.connection_status["qr_code"],
                    "qr_data": qr_code_data,
                    "qr_required": True
                }
            else:
                return {"error": "Failed to generate QR code"}
                
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}
    
    def check_connection_status(self) -> Dict:
        """Check current connection status for both Unipile and WhatsApp Business API"""
        status = {
            "unipile": {
                "available": bool(self.unipile_client),
                "connected": False,
                "accounts": []
            },
            "whatsapp_business": {
                "available": bool(self.phone_number_id and self.access_token),
                "connected": False,
                "phone_number_id": self.phone_number_id
            },
            "qr_code": self.connection_status.get("qr_code"),
            "last_check": datetime.now().isoformat()
        }
        
        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                whatsapp_accounts = [acc for acc in accounts.get("accounts", []) 
                                   if acc.get("provider") == "whatsapp"]
                status["unipile"]["connected"] = len(whatsapp_accounts) > 0
                status["unipile"]["accounts"] = whatsapp_accounts
                self.connection_status["unipile"] = status["unipile"]["connected"]
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")
        
        # Check WhatsApp Business API connection
        if self.phone_number_id and self.access_token:
            try:
                test_result = self._test_whatsapp_business_api()
                status["whatsapp_business"]["connected"] = test_result.get("success", False)
                self.connection_status["whatsapp_business"] = status["whatsapp_business"]["connected"]
            except Exception as e:
                self.logger.error(f"Error checking WhatsApp Business API status: {e}")
        
        self.connection_status["last_check"] = status["last_check"]
        return status
    
    def send_message(self, phone_number: str, message: str, **kwargs) -> Dict:
        """Send WhatsApp message via Unipile (primary) or WhatsApp Business API (fallback)"""
        # Clean phone number (remove + and spaces)
        phone_number = phone_number.replace("+", "").replace(" ", "").replace("-", "")
        
        # Try Unipile first
        if self.use_unipile and self.unipile_client:
            try:
                result = self.unipile_client.send_whatsapp_message(phone_number, message, **kwargs)
                if "error" not in result:
                    self.logger.info(f"WhatsApp message sent via Unipile to {phone_number}")
                    return {"success": True, "result": result, "method": "unipile"}
                else:
                    self.logger.warning(f"Unipile failed: {result.get('error')}, trying WhatsApp Business API")
            except Exception as e:
                self.logger.warning(f"Unipile error: {e}, trying WhatsApp Business API")
        
        # Fallback to WhatsApp Business API
        return self._send_via_business_api(phone_number, message, **kwargs)
    
    def _send_via_business_api(self, phone_number: str, message: str, **kwargs) -> Dict:
        """Send message via WhatsApp Business API"""
        if not self.phone_number_id or not self.access_token:
            return {"error": "WhatsApp Business API not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{self.phone_number_id}/messages"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "messaging_product": "whatsapp",
            "to": phone_number,
            "type": "text",
            "text": {"body": message}
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            self.logger.info(f"WhatsApp message sent via Business API to {phone_number}")
            return {
                "success": True,
                "result": result,
                "method": "whatsapp_business_api",
                "message_id": result.get("messages", [{}])[0].get("id")
            }
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"WhatsApp Business API error: {e}")
            return {"error": str(e)}
    
    def send_bulk_messages(self, recipients: List[Dict], message_template: str, delay: float = 2.0) -> List[Dict]:
        """Send WhatsApp messages to multiple recipients"""
        results = []
        
        for recipient in recipients:
            phone_number = recipient.get("phone_number") or recipient.get("contact")
            if not phone_number:
                continue
            
            # Personalize message
            try:
                personalized_message = message_template.format(**recipient)
            except KeyError:
                personalized_message = message_template
            
            result = self.send_message(phone_number, personalized_message)
            results.append({
                "phone_number": phone_number,
                "result": result,
                "method": result.get("method", "unknown"),
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def _test_whatsapp_business_api(self) -> Dict:
        """Test WhatsApp Business API connection"""
        if not self.phone_number_id or not self.access_token:
            return {"error": "WhatsApp Business API credentials not configured"}
        
        url = f"{self.base_url}/{self.phone_number_id}"
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return {"success": True, "data": response.json()}
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}
    
    def update_config(self, **kwargs) -> Dict:
        """Update WhatsApp configuration"""
        for key, value in kwargs.items():
            if key in ["phone_number_id", "access_token", "business_account_id", "webhook_verify_token"]:
                self.config[key] = value
                setattr(self, key, value)
        
        self._save_config()
        return {"success": True, "message": "Configuration updated"}
    
    def test_connection(self) -> Dict:
        """Test both Unipile and WhatsApp Business API connections"""
        results = {
            "unipile": {"available": False, "connected": False},
            "whatsapp_business": {"available": False, "connected": False}
        }
        
        # Test Unipile
        if self.unipile_client:
            results["unipile"]["available"] = True
            try:
                accounts = self.unipile_client.get_accounts()
                whatsapp_accounts = [acc for acc in accounts.get("accounts", []) 
                                   if acc.get("provider") == "whatsapp"]
                results["unipile"]["connected"] = len(whatsapp_accounts) > 0
            except Exception as e:
                results["unipile"]["error"] = str(e)
        
        # Test WhatsApp Business API
        if self.phone_number_id and self.access_token:
            results["whatsapp_business"]["available"] = True
            test_result = self._test_whatsapp_business_api()
            results["whatsapp_business"]["connected"] = test_result.get("success", False)
            if "error" in test_result:
                results["whatsapp_business"]["error"] = test_result["error"]
        
        return results
    
    def is_configured(self) -> bool:
        """Check if WhatsApp is properly configured"""
        return (self.unipile_client is not None) or (self.phone_number_id and self.access_token)

# Example usage
if __name__ == "__main__":
    whatsapp = WhatsAppMessaging()
    
    # Check connection status
    status = whatsapp.check_connection_status()
    print(f"Connection status: {status}")
    
    # Test authentication
    auth_result = whatsapp.authenticate_account()
    print(f"Authentication result: {auth_result}")
    
    # Example: Send message (uncomment to test)
    # result = whatsapp.send_message("+**********", "Hello from WhatsApp API!")
    # print(f"Message result: {result}")
    
    # Example: Send bulk messages (uncomment to test)
    # recipients = [
    #     {"phone_number": "+**********", "name": "John"},
    #     {"phone_number": "+**********", "name": "Jane"}
    # ]
    # bulk_result = whatsapp.send_bulk_messages(recipients, "Hello {name}!")
    # print(f"Bulk result: {bulk_result}")
