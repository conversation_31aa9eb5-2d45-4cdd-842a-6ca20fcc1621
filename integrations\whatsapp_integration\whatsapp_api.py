"""
WhatsApp Messaging Integration via Unipile API
Handles authentication and messaging for WhatsApp using Unipile
"""

import requests
import json
import time
from typing import Dict, List, Optional
import logging
from datetime import datetime
import base64
import qrcode
import io

class UnipileClient:
    """Unipile API client for WhatsApp integration"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api1.unipile.com:13115/api/v1"
        self.headers = {
            "X-API-KEY": self.api_key,
            "accept": "application/json",
            "Content-Type": "application/json"
        }

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to Unipile API"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json() if response.content else {"success": True}

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

class WhatsAppMessaging:
    def __init__(self, unipile_api_key: str = "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="):
        """Initialize WhatsApp messaging with Unipile API"""
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.phone_number = None
        self.connection_status = "disconnected"
        self.qr_code_data = None

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1  # 1 second between requests

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Check for existing WhatsApp account
        self._check_existing_account()
    
    def _check_existing_account(self):
        """Check for existing WhatsApp account in Unipile"""
        try:
            accounts = self.unipile.make_request("GET", "accounts")

            if "error" not in accounts and "items" in accounts:
                for account in accounts["items"]:
                    if account.get("type") == "WHATSAPP":
                        self.account_id = account.get("id")
                        self.phone_number = account.get("name")
                        self.connection_status = "connected"
                        self.logger.info(f"Found existing WhatsApp account: {self.phone_number}")
                        return

            self.connection_status = "disconnected"
            self.logger.info("No existing WhatsApp account found")

        except Exception as e:
            self.logger.error(f"Error checking existing account: {e}")
            self.connection_status = "error"

    def authenticate_account(self) -> Dict:
        """Generate QR code for WhatsApp authentication"""
        try:
            # Request QR code from Unipile
            auth_data = {
                "provider": "WHATSAPP",
                "return_qr": True
            }

            result = self.unipile.make_request("POST", "accounts/connect", auth_data)

            if "error" in result:
                self.logger.error(f"Authentication failed: {result['error']}")
                return {"error": result["error"]}

            # Extract QR code data
            if "qr_code" in result:
                self.qr_code_data = result["qr_code"]
                self.connection_status = "authenticating"

                return {
                    "success": True,
                    "qr_code": self.qr_code_data,
                    "status": "qr_generated",
                    "message": "Scan QR code with WhatsApp to authenticate"
                }
            else:
                return {"error": "QR code not received from Unipile"}

        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}

    def check_authentication_status(self) -> Dict:
        """Check current authentication status"""
        try:
            # Get current accounts to check if WhatsApp is connected
            accounts = self.unipile.make_request("GET", "accounts")

            if "error" in accounts:
                return {"status": "error", "error": accounts["error"]}

            # Look for WhatsApp account
            for account in accounts.get("items", []):
                if account.get("type") == "WHATSAPP":
                    self.account_id = account.get("id")
                    self.phone_number = account.get("name")
                    self.connection_status = "connected"

                    return {
                        "status": "connected",
                        "account_id": self.account_id,
                        "phone_number": self.phone_number,
                        "message": f"WhatsApp connected: {self.phone_number}"
                    }

            # No WhatsApp account found
            if self.connection_status == "authenticating":
                return {
                    "status": "authenticating",
                    "message": "Waiting for QR code scan..."
                }
            else:
                self.connection_status = "disconnected"
                return {
                    "status": "disconnected",
                    "message": "WhatsApp not connected"
                }

        except Exception as e:
            self.logger.error(f"Status check error: {e}")
            return {"status": "error", "error": str(e)}
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def send_message(self, phone_number: str, message: str) -> Dict:
        """Send message to WhatsApp number via Unipile"""
        if self.connection_status != "connected" or not self.account_id:
            return {
                "error": "WhatsApp not connected. Please authenticate first.",
                "status": self.connection_status
            }

        self._rate_limit()

        try:
            # Format phone number (ensure it starts with +)
            if not phone_number.startswith("+"):
                phone_number = "+" + phone_number.lstrip("+")

            # Send message via Unipile
            message_data = {
                "account_id": self.account_id,
                "to": phone_number,
                "text": message,
                "type": "text"
            }

            result = self.unipile.make_request("POST", "chats/messages", message_data)

            if "error" not in result:
                self.logger.info(f"Message sent successfully to {phone_number}")
                return {
                    "success": True,
                    "message_id": result.get("id"),
                    "to": phone_number,
                    "status": "sent"
                }
            else:
                self.logger.error(f"Failed to send message: {result}")
                return {"error": result["error"]}

        except Exception as e:
            self.logger.error(f"Message sending error: {e}")
            return {"error": str(e)}
    

    
    def send_bulk_messages(self, recipients: List[str], message: str, delay: float = 1.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        if self.connection_status != "connected" or not self.account_id:
            return [{
                "error": "WhatsApp not connected. Please authenticate first.",
                "status": self.connection_status
            }]

        results = []

        for phone_number in recipients:
            try:
                result = self.send_message(phone_number, message)
                results.append({
                    "phone_number": phone_number,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })

                # Add delay between messages
                if delay > 0:
                    time.sleep(delay)

            except Exception as e:
                results.append({
                    "phone_number": phone_number,
                    "result": {"error": str(e)},
                    "timestamp": datetime.now().isoformat()
                })

        self.logger.info(f"Bulk messaging completed: {len(results)} messages processed")
        return results

    def get_account_info(self) -> Dict:
        """Get WhatsApp account information"""
        if not self.account_id:
            return {"error": "No WhatsApp account connected"}

        try:
            result = self.unipile.make_request("GET", f"accounts/{self.account_id}")

            if "error" not in result:
                return {
                    "account_id": self.account_id,
                    "phone_number": self.phone_number,
                    "status": self.connection_status,
                    "account_details": result
                }
            else:
                return {"error": result["error"]}

        except Exception as e:
            return {"error": str(e)}

    def disconnect_account(self) -> Dict:
        """Disconnect WhatsApp account"""
        if not self.account_id:
            return {"error": "No account to disconnect"}

        try:
            result = self.unipile.make_request("DELETE", f"accounts/{self.account_id}")

            if "error" not in result:
                self.account_id = None
                self.phone_number = None
                self.connection_status = "disconnected"
                self.qr_code_data = None

                return {
                    "success": True,
                    "message": "WhatsApp account disconnected successfully"
                }
            else:
                return {"error": result["error"]}

        except Exception as e:
            return {"error": str(e)}

    def is_configured(self) -> bool:
        """Check if WhatsApp is properly configured"""
        return self.connection_status == "connected" and bool(self.account_id)

# Example usage
if __name__ == "__main__":
    whatsapp = WhatsAppMessaging()

    # Check authentication status
    status = whatsapp.check_authentication_status()
    print(f"Authentication status: {status}")

    if whatsapp.is_configured():
        # Example: Send a test message
        result = whatsapp.send_message("+**********", "Hello from WhatsApp via Unipile!")
        print(f"Message result: {result}")

        # Example: Send bulk messages
        recipients = ["+**********", "+**********"]
        bulk_results = whatsapp.send_bulk_messages(recipients, "Bulk message test")
        print(f"Bulk message results: {bulk_results}")
    else:
        print("WhatsApp not connected. Use authenticate_account() to connect.")
