"""
Example usage of Unipile API integration for social media messaging
"""

from unipile_api import UnipileAPI
import json

def main():
    print("🚀 Unipile API Integration Example")
    print("=" * 50)
    
    # Initialize Unipile API
    unipile = UnipileAPI()
    
    # 1. Check connection and get accounts
    print("\n📊 Getting connected accounts...")
    accounts = unipile.get_accounts()
    
    if "error" in accounts:
        print(f"❌ Error: {accounts['error']}")
        return
    
    print("✅ Connected accounts:")
    for account in accounts.get("items", []):
        platform = account.get("type", "Unknown")
        name = account.get("name", "Unknown")
        account_id = account.get("id", "Unknown")
        print(f"  📱 {platform}: {name} (ID: {account_id})")
    
    # 2. Check platform status
    print("\n🔍 Platform connection status:")
    status = unipile.get_connection_status()
    for platform, connected in status.items():
        emoji = "✅" if connected else "❌"
        print(f"  {emoji} {platform.capitalize()}: {'Connected' if connected else 'Not Connected'}")
    
    # 3. Get platform-specific accounts
    print("\n📱 Platform accounts:")
    platform_accounts = unipile.get_platform_accounts()
    
    if "error" not in platform_accounts:
        for platform, account in platform_accounts.items():
            if account:
                name = account.get("name", account.get("id", "Unknown"))
                print(f"  ✅ {platform.capitalize()}: {name}")
            else:
                print(f"  ❌ {platform.capitalize()}: Not connected")
    
    # 4. Example messaging (commented out to avoid sending actual messages)
    print("\n💬 Example messaging code:")
    print("""
    # Send WhatsApp message
    if unipile.is_platform_connected("whatsapp"):
        result = unipile.send_whatsapp_message("+**********", "Hello from Unipile!")
        print(f"WhatsApp result: {result}")
    
    # Send Telegram message  
    if unipile.is_platform_connected("telegram"):
        result = unipile.send_telegram_message("@username", "Hello from Unipile!")
        print(f"Telegram result: {result}")
    
    # Send Facebook message
    if unipile.is_platform_connected("facebook"):
        result = unipile.send_facebook_message("user_id", "Hello from Unipile!")
        print(f"Facebook result: {result}")
    
    # Send Instagram message
    if unipile.is_platform_connected("instagram"):
        result = unipile.send_instagram_message("user_id", "Hello from Unipile!")
        print(f"Instagram result: {result}")
    
    # Send LinkedIn message
    if unipile.is_platform_connected("linkedin"):
        result = unipile.send_linkedin_message("person_id", "Hello from Unipile!")
        print(f"LinkedIn result: {result}")
    """)
    
    # 5. Bulk messaging example
    print("\n📤 Bulk messaging example:")
    print("""
    # Prepare bulk messages
    messages = [
        {
            "account_id": "whatsapp_account_id",
            "recipient_id": "+**********", 
            "text": "Hello from WhatsApp!"
        },
        {
            "account_id": "telegram_account_id",
            "recipient_id": "@username",
            "text": "Hello from Telegram!"
        }
    ]
    
    # Send bulk messages with delay
    results = unipile.send_bulk_messages(messages, delay=2.0)
    for result in results:
        print(f"Result: {result}")
    """)
    
    print("\n✅ Integration is working correctly!")
    print("\n💡 Next steps:")
    print("  1. Connect more platforms via Unipile dashboard")
    print("  2. Test messaging with actual recipients")
    print("  3. Use the unified messaging system for bulk operations")
    print("  4. Open unipile_setup.html for detailed setup instructions")

if __name__ == "__main__":
    main()
