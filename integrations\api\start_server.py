#!/usr/bin/env python3
"""
Unified Messaging API Server Startup Script
Launches either Flask or FastAPI server with configuration options
"""

import argparse
import sys
import os
import logging
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging(level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('api_server.log')
        ]
    )

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = {
        'flask': 'Flask',
        'fastapi': 'FastAPI',
        'uvicorn': 'uvicorn (for FastAPI)',
        'requests': 'requests',
        'flask_cors': 'flask-cors'
    }
    
    missing_packages = []
    
    for package, display_name in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(display_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install flask fastapi uvicorn requests flask-cors")
        return False
    
    return True

def start_flask_server(host: str = "0.0.0.0", port: int = 5000, debug: bool = False):
    """Start Flask server"""
    print("🚀 Starting Flask server...")
    print(f"📍 Server will be available at: http://{host}:{port}")
    print(f"📖 API documentation: http://{host}:{port}/api/health")
    
    try:
        from messaging_endpoints import app
        app.run(host=host, port=port, debug=debug)
    except ImportError as e:
        print(f"❌ Failed to import Flask app: {e}")
        return False
    except Exception as e:
        print(f"❌ Failed to start Flask server: {e}")
        return False

def start_fastapi_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Start FastAPI server"""
    print("🚀 Starting FastAPI server...")
    print(f"📍 Server will be available at: http://{host}:{port}")
    print(f"📖 API documentation: http://{host}:{port}/docs")
    print(f"📖 Alternative docs: http://{host}:{port}/redoc")
    
    try:
        import uvicorn
        uvicorn.run(
            "fastapi_endpoints:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except ImportError:
        print("❌ uvicorn not installed. Install with: pip install uvicorn")
        return False
    except Exception as e:
        print(f"❌ Failed to start FastAPI server: {e}")
        return False

def check_configuration():
    """Check if the system is properly configured"""
    print("🔍 Checking system configuration...")
    
    # Check if unified messaging can be initialized
    try:
        from unified_messaging import UnifiedMessaging
        um = UnifiedMessaging()
        print("✅ Unified messaging system initialized")
        
        # Check platform status
        status = um.get_platform_status()
        configured_platforms = [p for p, s in status.items() if s.get('configured', False)]
        
        if configured_platforms:
            print(f"✅ Configured platforms: {', '.join(configured_platforms)}")
        else:
            print("⚠️  No platforms configured yet")
            
    except Exception as e:
        print(f"⚠️  Unified messaging initialization warning: {e}")
    
    # Check Unipile configuration
    try:
        from unipile_config import is_unipile_available
        if is_unipile_available():
            print("✅ Unipile API configured")
        else:
            print("⚠️  Unipile API not configured")
            print("💡 Set UNIPILE_API_KEY environment variable or update unipile_config.json")
    except Exception as e:
        print(f"⚠️  Unipile check warning: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Unified Messaging API Server")
    parser.add_argument(
        "--framework", 
        choices=["flask", "fastapi"], 
        default="flask",
        help="Web framework to use (default: flask)"
    )
    parser.add_argument(
        "--host", 
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int,
        help="Port to bind to (default: 5000 for Flask, 8000 for FastAPI)"
    )
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="Enable debug mode (Flask only)"
    )
    parser.add_argument(
        "--reload", 
        action="store_true",
        help="Enable auto-reload (FastAPI only)"
    )
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    parser.add_argument(
        "--check-only", 
        action="store_true",
        help="Only check configuration, don't start server"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    print("🌟 Unified Messaging API Server")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check configuration
    check_configuration()
    
    if args.check_only:
        print("\n✅ Configuration check completed")
        return
    
    # Set default ports
    if args.port is None:
        args.port = 5000 if args.framework == "flask" else 8000
    
    print(f"\n🚀 Starting {args.framework.upper()} server...")
    print(f"📍 Host: {args.host}")
    print(f"📍 Port: {args.port}")
    
    # Start appropriate server
    try:
        if args.framework == "flask":
            start_flask_server(args.host, args.port, args.debug)
        elif args.framework == "fastapi":
            start_fastapi_server(args.host, args.port, args.reload)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
