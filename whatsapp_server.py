"""
Flask server for WhatsApp integration with Unipile API
Provides REST API endpoints for the WhatsApp authentication HTML interface
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path to import whatsapp_api
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from integrations.whatsapp_integration.whatsapp_api import WhatsAppMessaging

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global WhatsApp instance
whatsapp_client = None

def get_whatsapp_client():
    """Get or create WhatsApp client instance"""
    global whatsapp_client
    if whatsapp_client is None:
        whatsapp_client = WhatsAppMessaging()
    return whatsapp_client

@app.route('/')
def index():
    """Serve the WhatsApp authentication HTML page"""
    return send_from_directory('.', 'whatsapp_auth.html')

@app.route('/api/whatsapp/authenticate', methods=['POST'])
def authenticate():
    """Generate QR code for WhatsApp authentication"""
    try:
        client = get_whatsapp_client()
        result = client.authenticate_account()
        
        logger.info(f"Authentication request result: {result}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/whatsapp/status', methods=['GET'])
def check_status():
    """Check WhatsApp authentication status"""
    try:
        client = get_whatsapp_client()
        result = client.check_authentication_status()
        
        logger.info(f"Status check result: {result}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/whatsapp/disconnect', methods=['POST'])
def disconnect():
    """Disconnect WhatsApp account"""
    try:
        client = get_whatsapp_client()
        result = client.disconnect_account()
        
        logger.info(f"Disconnect result: {result}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Disconnect error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/whatsapp/send-test', methods=['POST'])
def send_test_message():
    """Send a test message"""
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        message = data.get('message')
        
        if not phone_number or not message:
            return jsonify({"error": "Phone number and message are required"}), 400
        
        client = get_whatsapp_client()
        result = client.send_message(phone_number, message)
        
        logger.info(f"Test message result: {result}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Send test message error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/whatsapp/send-bulk', methods=['POST'])
def send_bulk_messages():
    """Send bulk messages"""
    try:
        data = request.get_json()
        recipients = data.get('recipients', [])
        message = data.get('message')
        delay = data.get('delay', 1.0)
        
        if not recipients or not message:
            return jsonify({"error": "Recipients and message are required"}), 400
        
        client = get_whatsapp_client()
        results = client.send_bulk_messages(recipients, message, delay)
        
        logger.info(f"Bulk message results: {len(results)} messages processed")
        return jsonify({"success": True, "results": results})
        
    except Exception as e:
        logger.error(f"Send bulk messages error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/whatsapp/account-info', methods=['GET'])
def get_account_info():
    """Get WhatsApp account information"""
    try:
        client = get_whatsapp_client()
        result = client.get_account_info()
        
        logger.info(f"Account info result: {result}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Get account info error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "WhatsApp Integration Server"
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    print("🚀 Starting WhatsApp Integration Server...")
    print("📱 WhatsApp authentication interface will be available at: http://localhost:5000")
    print("🔗 API endpoints:")
    print("   - POST /api/whatsapp/authenticate - Generate QR code")
    print("   - GET  /api/whatsapp/status - Check authentication status")
    print("   - POST /api/whatsapp/disconnect - Disconnect account")
    print("   - POST /api/whatsapp/send-test - Send test message")
    print("   - POST /api/whatsapp/send-bulk - Send bulk messages")
    print("   - GET  /api/whatsapp/account-info - Get account information")
    print("   - GET  /health - Health check")
    print("\n💡 Open http://localhost:5000 in your browser to access the WhatsApp interface")
    
    # Run the Flask app
    app.run(host='0.0.0.0', port=5000, debug=True)
