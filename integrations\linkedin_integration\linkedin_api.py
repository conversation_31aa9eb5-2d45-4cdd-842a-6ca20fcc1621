"""
LinkedIn API Integration
Handles authentication and messaging for LinkedIn Business/Personal accounts
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime
import urllib.parse

class LinkedInAPI:
    def __init__(self, config_path: str = "integrations/linkedin_integration/config.json"):
        """Initialize LinkedIn API client"""
        self.config_path = config_path
        self.config = self._load_config()
        self.client_id = self.config.get("client_id")
        self.client_secret = self.config.get("client_secret")
        self.access_token = self.config.get("access_token")
        self.refresh_token = self.config.get("refresh_token")
        self.person_id = self.config.get("person_id")
        self.api_version = self.config.get("api_version", "v2")
        self.base_url = f"{self.config.get('base_url', 'https://api.linkedin.com')}/{self.api_version}"
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("requests_per_second", 10)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, 
                     params: Dict = None, headers: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.access_token:
            return {"error": "Access token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        # Default headers
        default_headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "X-Restli-Protocol-Version": "2.0.0"
        }
        
        if headers:
            default_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=default_headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json() if response.content else {"success": True}
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def get_profile(self, fields: List[str] = None) -> Dict:
        """Get user profile information"""
        if not fields:
            fields = ["id", "firstName", "lastName", "headline", "profilePicture", "industry", "summary"]
        
        params = {
            "projection": f"({','.join(fields)})"
        }
        
        return self._make_request("GET", "people/~", params=params)
    
    def send_message(self, recipient_id: str, subject: str, message_body: str) -> Dict:
        """Send direct message to LinkedIn connection"""
        if not self.person_id:
            return {"error": "Person ID not configured"}
        
        data = {
            "recipients": [f"urn:li:person:{recipient_id}"],
            "subject": subject,
            "body": message_body,
            "sender": f"urn:li:person:{self.person_id}"
        }
        
        result = self._make_request("POST", "messaging/conversations", data)
        
        if "error" not in result:
            self.logger.info(f"Message sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send message: {result}")
        
        return result
    
    def get_connections(self, start: int = 0, count: int = 50) -> Dict:
        """Get user's connections"""
        params = {
            "start": start,
            "count": count,
            "projection": "(elements*(to~(id,firstName,lastName,headline,profilePicture)))"
        }
        
        return self._make_request("GET", "people/~/connections", params=params)
    
    def send_connection_request(self, person_id: str, message: str = None) -> Dict:
        """Send connection request to a person"""
        if not message:
            message = self.config.get("connection_request_templates", {}).get("general", "I'd like to connect with you.")
        
        data = {
            "invitee": {
                "com.linkedin.voyager.growth.invitation.InviteeProfile": {
                    "profileId": person_id
                }
            },
            "message": message
        }
        
        result = self._make_request("POST", "people/~/mailbox", data)
        
        if "error" not in result:
            self.logger.info(f"Connection request sent to {person_id}")
        else:
            self.logger.error(f"Failed to send connection request: {result}")
        
        return result
    
    def create_post(self, content: str, visibility: str = None, 
                   media_url: str = None, link_url: str = None) -> Dict:
        """Create a LinkedIn post"""
        if not visibility:
            visibility = self.config.get("post_settings", {}).get("default_visibility", "PUBLIC")
        
        max_length = self.config.get("post_settings", {}).get("max_content_length", 3000)
        if len(content) > max_length:
            content = content[:max_length-3] + "..."
        
        post_data = {
            "author": f"urn:li:person:{self.person_id}",
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {
                        "text": content
                    },
                    "shareMediaCategory": "NONE"
                }
            },
            "visibility": {
                "com.linkedin.ugc.MemberNetworkVisibility": visibility
            }
        }
        
        # Add media if provided
        if media_url:
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["shareMediaCategory"] = "IMAGE"
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["media"] = [{
                "status": "READY",
                "media": media_url
            }]
        
        # Add link if provided
        if link_url:
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["shareMediaCategory"] = "ARTICLE"
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["media"] = [{
                "status": "READY",
                "originalUrl": link_url
            }]
        
        result = self._make_request("POST", "ugcPosts", post_data)
        
        if "error" not in result:
            self.logger.info("LinkedIn post created successfully")
        else:
            self.logger.error(f"Failed to create post: {result}")
        
        return result
    
    def get_company_info(self, company_id: str, fields: List[str] = None) -> Dict:
        """Get company information"""
        if not fields:
            fields = ["id", "name", "description", "industry", "website", "employeeCountRange", "foundedOn"]
        
        params = {
            "projection": f"({','.join(fields)})"
        }
        
        return self._make_request("GET", f"companies/{company_id}", params=params)
    
    def search_people(self, keywords: str = None, company: str = None, 
                     industry: str = None, location: str = None, count: int = 25) -> Dict:
        """Search for people on LinkedIn"""
        params = {
            "count": count,
            "start": 0
        }
        
        # Build search query
        query_parts = []
        if keywords:
            query_parts.append(f"keywords:{keywords}")
        if company:
            query_parts.append(f"company:{company}")
        if industry:
            query_parts.append(f"industry:{industry}")
        if location:
            query_parts.append(f"location:{location}")
        
        if query_parts:
            params["q"] = "people"
            params["keywords"] = " ".join(query_parts)
        
        return self._make_request("GET", "peopleSearch", params=params)
    
    def get_post_analytics(self, post_id: str) -> Dict:
        """Get analytics for a specific post"""
        params = {
            "q": "ugcPost",
            "ugcPost": f"urn:li:ugcPost:{post_id}"
        }
        
        return self._make_request("GET", "socialActions", params=params)
    
    def like_post(self, post_id: str) -> Dict:
        """Like a LinkedIn post"""
        data = {
            "actor": f"urn:li:person:{self.person_id}",
            "object": f"urn:li:ugcPost:{post_id}"
        }
        
        result = self._make_request("POST", "socialActions", data)
        
        if "error" not in result:
            self.logger.info(f"Post {post_id} liked successfully")
        else:
            self.logger.error(f"Failed to like post: {result}")
        
        return result
    
    def comment_on_post(self, post_id: str, comment_text: str) -> Dict:
        """Comment on a LinkedIn post"""
        data = {
            "actor": f"urn:li:person:{self.person_id}",
            "object": f"urn:li:ugcPost:{post_id}",
            "message": {
                "text": comment_text
            }
        }
        
        result = self._make_request("POST", "socialActions", data)
        
        if "error" not in result:
            self.logger.info(f"Comment posted on {post_id}")
        else:
            self.logger.error(f"Failed to comment on post: {result}")
        
        return result
    
    def send_bulk_messages(self, recipients: List[Dict], subject: str, 
                          message_template: str, delay: float = 3.0) -> List[Dict]:
        """Send messages to multiple recipients with personalization"""
        results = []
        
        for recipient in recipients:
            recipient_id = recipient.get("id")
            if not recipient_id:
                continue
            
            # Personalize message
            personalized_message = message_template.format(**recipient)
            
            result = self.send_message(recipient_id, subject, personalized_message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, recipient_id: str, template_name: str, 
                            subject: str = None, **kwargs) -> Dict:
        """Send predefined template message"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}
        
        message = templates[template_name].format(**kwargs)
        
        if not subject:
            subject = f"Message from {self.get_profile().get('firstName', 'LinkedIn User')}"
        
        return self.send_message(recipient_id, subject, message)
    
    def refresh_access_token(self) -> Dict:
        """Refresh access token using refresh token"""
        if not self.refresh_token or not self.client_id or not self.client_secret:
            return {"error": "Refresh token or client credentials not configured"}
        
        data = {
            "grant_type": "refresh_token",
            "refresh_token": self.refresh_token,
            "client_id": self.client_id,
            "client_secret": self.client_secret
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            response = requests.post("https://www.linkedin.com/oauth/v2/accessToken", 
                                   data=data, headers=headers)
            response.raise_for_status()
            result = response.json()
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                self.config["access_token"] = self.access_token
                
                if "refresh_token" in result:
                    self.refresh_token = result["refresh_token"]
                    self.config["refresh_token"] = self.refresh_token
                
                self._save_config()
                self.logger.info("Access token refreshed successfully")
            
            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Token refresh failed: {e}")
            return {"error": str(e)}
    
    def is_configured(self) -> bool:
        """Check if LinkedIn API is properly configured"""
        return bool(self.access_token and self.client_id)
    
    def update_config(self, client_id: str = None, client_secret: str = None,
                     access_token: str = None, refresh_token: str = None, person_id: str = None):
        """Update configuration"""
        if client_id:
            self.config["client_id"] = client_id
            self.client_id = client_id
        
        if client_secret:
            self.config["client_secret"] = client_secret
            self.client_secret = client_secret
        
        if access_token:
            self.config["access_token"] = access_token
            self.access_token = access_token
        
        if refresh_token:
            self.config["refresh_token"] = refresh_token
            self.refresh_token = refresh_token
        
        if person_id:
            self.config["person_id"] = person_id
            self.person_id = person_id
        
        self._save_config()
        self.logger.info("LinkedIn configuration updated")

# Example usage
if __name__ == "__main__":
    linkedin = LinkedInAPI()
    
    # Check if configured
    if not linkedin.is_configured():
        print("LinkedIn API not configured. Please update config.json with your credentials.")
    else:
        # Example: Get profile info
        profile = linkedin.get_profile()
        print(f"Profile: {profile}")
        
        # Example: Send a test message (replace with actual person ID)
        # result = linkedin.send_message("PERSON_ID", "Test Subject", "Hello from LinkedIn API!")
        # print(f"Message result: {result}")
