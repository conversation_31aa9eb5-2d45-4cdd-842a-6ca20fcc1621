<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Authentication - Unipile Integration</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #25d366, #128c7e);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        h1 {
            color: #25d366;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .auth-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .qr-section {
            text-align: center;
            padding: 20px;
            border: 2px solid #25d366;
            border-radius: 15px;
            background: #f8fff8;
        }
        .qr-code-area {
            width: 250px;
            height: 250px;
            border: 2px dashed #25d366;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 10px;
            position: relative;
        }
        .qr-placeholder {
            color: #666;
            font-size: 14px;
            text-align: center;
        }
        .status-section {
            padding: 20px;
        }
        .status-indicator {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .status-disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        .status-authenticating {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        .status-connected {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        .account-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        button {
            background: linear-gradient(135deg, #25d366, #128c7e);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .test-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="tel"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            border-color: #25d366;
            outline: none;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #25d366;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        @media (max-width: 768px) {
            .auth-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 WhatsApp Authentication</h1>
        <p style="text-align: center; font-size: 1.2em; color: #666;">
            Connect your WhatsApp account via Unipile API
        </p>
        
        <div class="info">
            <strong>🔗 Unipile Integration:</strong> This uses the Unipile API to connect your WhatsApp account.
            Simply scan the QR code with your WhatsApp mobile app to authenticate.
            <br><br>
            <strong>📊 Real-time Features:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>✅ QR code display area with automatic generation</li>
                <li>🔄 Authentication status indicator with real-time updates</li>
                <li>📱 Account connection interface with disconnect option</li>
                <li>⏱️ Real-time connection status monitoring</li>
            </ul>
        </div>

        <div class="auth-container">
            <!-- QR Code Section -->
            <div class="qr-section">
                <h3>📷 QR Code Authentication</h3>
                <div class="qr-code-area" id="qrCodeArea">
                    <div class="qr-placeholder" id="qrPlaceholder">
                        Click "Generate QR Code" to start authentication
                    </div>
                </div>
                <button onclick="generateQRCode()" id="generateBtn">Generate QR Code</button>
                <button onclick="refreshQRCode()" id="refreshBtn" style="display: none;">Refresh QR Code</button>
            </div>

            <!-- Status Section -->
            <div class="status-section">
                <h3>📊 Connection Status</h3>
                <div class="status-indicator status-disconnected" id="statusIndicator">
                    ❌ Disconnected
                </div>
                
                <div class="account-info" id="accountInfo" style="display: none;">
                    <h4>📱 Account Information</h4>
                    <p><strong>Phone Number:</strong> <span id="phoneNumber">-</span></p>
                    <p><strong>Account ID:</strong> <span id="accountId">-</span></p>
                    <p><strong>Status:</strong> <span id="connectionStatus">-</span></p>
                </div>
                
                <button onclick="checkStatus()" id="checkStatusBtn">Check Status</button>
                <button onclick="disconnectAccount()" id="disconnectBtn" style="display: none;">Disconnect</button>
            </div>
        </div>

        <!-- Real-time Status Updates -->
        <div class="info">
            <strong>🔄 Real-time Updates:</strong> Status will automatically refresh every 5 seconds during authentication.
            <span id="autoRefreshStatus">Auto-refresh: OFF</span>
        </div>

        <!-- Test Messaging Section -->
        <div class="test-section" id="testSection" style="display: none;">
            <h3>💬 Test Messaging</h3>

            <!-- Single Message -->
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                <h4>📱 Single Message</h4>
                <div class="form-group">
                    <label for="testPhone">Phone Number (with country code):</label>
                    <input type="tel" id="testPhone" placeholder="+**********">
                </div>
                <div class="form-group">
                    <label for="testMessage">Test Message:</label>
                    <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from WhatsApp via Unipile."></textarea>
                </div>
                <button onclick="sendTestMessage()">Send Test Message</button>
                <div id="testResult"></div>
            </div>

            <!-- Bulk Messaging -->
            <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                <h4>📢 Bulk Messaging</h4>
                <div class="form-group">
                    <label for="bulkPhones">Phone Numbers (one per line, with country code):</label>
                    <textarea id="bulkPhones" rows="4" placeholder="+**********&#10;+0987654321&#10;+1122334455"></textarea>
                </div>
                <div class="form-group">
                    <label for="bulkMessage">Bulk Message:</label>
                    <textarea id="bulkMessage" rows="3" placeholder="Hello! This is a bulk message from WhatsApp via Unipile."></textarea>
                </div>
                <div class="form-group">
                    <label for="bulkDelay">Delay between messages (seconds):</label>
                    <input type="number" id="bulkDelay" value="1" min="0.5" max="10" step="0.5">
                </div>
                <button onclick="sendBulkMessages()">Send Bulk Messages</button>
                <div id="bulkResult"></div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="info">
            <h4>📋 Instructions:</h4>
            <ol>
                <li>Click "Generate QR Code" to create a new authentication QR code</li>
                <li>Open WhatsApp on your mobile device</li>
                <li>Go to Settings → Linked Devices → Link a Device</li>
                <li>Scan the QR code displayed above</li>
                <li>Wait for the connection to be established</li>
                <li>Once connected, you can send test messages</li>
            </ol>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAuthenticated = false;

        // Check initial status on page load
        window.addEventListener('load', function() {
            checkStatus();
        });

        function generateQRCode() {
            const generateBtn = document.getElementById('generateBtn');
            const qrPlaceholder = document.getElementById('qrPlaceholder');
            
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<span class="loading"></span> Generating...';
            qrPlaceholder.innerHTML = 'Generating QR code...';
            
            fetch('/api/whatsapp/authenticate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.qr_code) {
                    displayQRCode(data.qr_code);
                    updateStatus('authenticating', 'Waiting for QR code scan...');
                    startAutoRefresh();
                    
                    document.getElementById('refreshBtn').style.display = 'inline-block';
                } else {
                    qrPlaceholder.innerHTML = 'Failed to generate QR code: ' + (data.error || 'Unknown error');
                    updateStatus('error', data.error || 'QR generation failed');
                }
            })
            .catch(error => {
                qrPlaceholder.innerHTML = 'Error: ' + error.message;
                updateStatus('error', error.message);
            })
            .finally(() => {
                generateBtn.disabled = false;
                generateBtn.innerHTML = 'Generate QR Code';
            });
        }

        function displayQRCode(qrCodeData) {
            const qrArea = document.getElementById('qrCodeArea');
            qrArea.innerHTML = `<img src="data:image/png;base64,${qrCodeData}" alt="WhatsApp QR Code" style="max-width: 100%; max-height: 100%;">`;
        }

        function refreshQRCode() {
            generateQRCode();
        }

        function checkStatus() {
            fetch('/api/whatsapp/status')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'connected') {
                    updateStatus('connected', `Connected: ${data.phone_number}`);
                    showAccountInfo(data);
                    showTestSection();
                    stopAutoRefresh();
                    isAuthenticated = true;
                } else if (data.status === 'authenticating') {
                    updateStatus('authenticating', 'Waiting for QR code scan...');
                } else if (data.status === 'error') {
                    updateStatus('error', data.error || 'Connection error');
                    stopAutoRefresh();
                } else {
                    updateStatus('disconnected', 'Not connected');
                    hideAccountInfo();
                    hideTestSection();
                    stopAutoRefresh();
                    isAuthenticated = false;
                }
            })
            .catch(error => {
                updateStatus('error', 'Status check failed: ' + error.message);
            });
        }

        function updateStatus(status, message) {
            const indicator = document.getElementById('statusIndicator');
            const statusClasses = ['status-disconnected', 'status-authenticating', 'status-connected', 'status-error'];
            
            // Remove all status classes
            statusClasses.forEach(cls => indicator.classList.remove(cls));
            
            // Add appropriate class and update content
            switch(status) {
                case 'connected':
                    indicator.classList.add('status-connected');
                    indicator.innerHTML = '✅ ' + message;
                    break;
                case 'authenticating':
                    indicator.classList.add('status-authenticating');
                    indicator.innerHTML = '⏳ ' + message;
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    indicator.innerHTML = '❌ ' + message;
                    break;
                default:
                    indicator.classList.add('status-disconnected');
                    indicator.innerHTML = '❌ ' + message;
            }
        }

        function showAccountInfo(data) {
            document.getElementById('accountInfo').style.display = 'block';
            document.getElementById('phoneNumber').textContent = data.phone_number || '-';
            document.getElementById('accountId').textContent = data.account_id || '-';
            document.getElementById('connectionStatus').textContent = data.status || '-';
            document.getElementById('disconnectBtn').style.display = 'inline-block';
        }

        function hideAccountInfo() {
            document.getElementById('accountInfo').style.display = 'none';
            document.getElementById('disconnectBtn').style.display = 'none';
        }

        function showTestSection() {
            document.getElementById('testSection').style.display = 'block';
        }

        function hideTestSection() {
            document.getElementById('testSection').style.display = 'none';
        }

        function startAutoRefresh() {
            if (autoRefreshInterval) return;
            
            autoRefreshInterval = setInterval(checkStatus, 5000);
            document.getElementById('autoRefreshStatus').textContent = 'Auto-refresh: ON';
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                document.getElementById('autoRefreshStatus').textContent = 'Auto-refresh: OFF';
            }
        }

        function disconnectAccount() {
            if (!confirm('Are you sure you want to disconnect your WhatsApp account?')) {
                return;
            }
            
            fetch('/api/whatsapp/disconnect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatus('disconnected', 'Account disconnected successfully');
                    hideAccountInfo();
                    hideTestSection();
                    document.getElementById('qrCodeArea').innerHTML = '<div class="qr-placeholder">Click "Generate QR Code" to start authentication</div>';
                    document.getElementById('refreshBtn').style.display = 'none';
                } else {
                    alert('Failed to disconnect: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Disconnect error: ' + error.message);
            });
        }

        function sendTestMessage() {
            const phone = document.getElementById('testPhone').value;
            const message = document.getElementById('testMessage').value;

            if (!phone || !message) {
                document.getElementById('testResult').innerHTML =
                    '<div class="error">Please enter both phone number and message</div>';
                return;
            }

            fetch('/api/whatsapp/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phone_number: phone,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send message: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function sendBulkMessages() {
            const phonesText = document.getElementById('bulkPhones').value;
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('bulkDelay').value);

            if (!phonesText || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">Please enter both phone numbers and message</div>';
                return;
            }

            // Parse phone numbers (one per line)
            const recipients = phonesText.split('\n')
                .map(phone => phone.trim())
                .filter(phone => phone.length > 0);

            if (recipients.length === 0) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">Please enter at least one phone number</div>';
                return;
            }

            document.getElementById('bulkResult').innerHTML =
                '<div class="info">📤 Sending bulk messages... Please wait.</div>';

            fetch('/api/whatsapp/send-bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results;
                    const successful = results.filter(r => r.result.success).length;
                    const failed = results.length - successful;

                    let resultHtml = `<div class="success">✅ Bulk messaging completed!</div>`;
                    resultHtml += `<div class="info">📊 Results: ${successful} sent, ${failed} failed</div>`;

                    if (failed > 0) {
                        resultHtml += '<div class="error">❌ Failed messages:<ul>';
                        results.forEach(r => {
                            if (!r.result.success) {
                                resultHtml += `<li>${r.phone_number}: ${r.result.error}</li>`;
                            }
                        });
                        resultHtml += '</ul></div>';
                    }

                    resultDiv.innerHTML = resultHtml;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send bulk messages: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
