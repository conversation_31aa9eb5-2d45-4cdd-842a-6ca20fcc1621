<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{PLATFORM_NAME}} Authentication - Unified Messaging</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .auth-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }

        .auth-section h2 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-danger {
            background: #dc3545;
        }

        .qr-code-container {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 10px;
            border: 2px dashed #e1e5e9;
            margin: 20px 0;
        }

        .qr-code {
            max-width: 256px;
            margin: 0 auto;
        }

        .qr-placeholder {
            width: 256px;
            height: 256px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border-radius: 10px;
            color: #6c757d;
            font-size: 18px;
        }

        .result-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e1e5e9;
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .platform-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-dot.connected {
            background: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3);
        }

        .status-dot.disconnected {
            background: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
        }

        .status-dot.loading {
            background: #ffc107;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .auth-section {
                padding: 20px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="platform-icon">{{PLATFORM_ICON}}</span>{{PLATFORM_NAME}} Authentication</h1>
            <p>Connect your {{PLATFORM_NAME}} account to the Unified Messaging System</p>
        </div>

        <div class="content">
            <!-- Connection Status -->
            <div class="auth-section">
                <h2>🔗 Connection Status</h2>
                <div id="connectionStatus" class="status-indicator status-loading">
                    <span class="loading-spinner"></span> Checking connection status...
                </div>
                <button class="btn" onclick="checkConnectionStatus()">Refresh Status</button>
            </div>

            <!-- Unipile Authentication -->
            <div class="auth-section">
                <h2>🚀 Unipile API Authentication (Recommended)</h2>
                <div class="info">
                    <strong>Unipile provides unified access to {{PLATFORM_NAME}} with simplified authentication and enhanced features.</strong>
                </div>
                
                <div class="form-group">
                    <label for="unipileApiKey">Unipile API Key:</label>
                    <input type="password" id="unipileApiKey" placeholder="Your Unipile API key">
                </div>
                
                <button class="btn" onclick="connectUnipile()">Connect via Unipile</button>
                <button class="btn btn-secondary" onclick="testUnipileConnection()">Test Connection</button>
                
                <div id="unipileResult"></div>
            </div>

            <!-- QR Code Authentication (if applicable) -->
            <div class="auth-section" id="qrAuthSection">
                <h2>📱 QR Code Authentication</h2>
                <div class="info">
                    <strong>Scan the QR code with your {{PLATFORM_NAME}} app to authenticate.</strong>
                </div>
                
                <div class="qr-code-container">
                    <div id="qrCodeArea" class="qr-placeholder">
                        QR Code will appear here
                    </div>
                </div>
                
                <button class="btn" onclick="generateQRCode()">Generate QR Code</button>
                <button class="btn btn-secondary" onclick="refreshQRCode()">Refresh QR Code</button>
                
                <div id="qrResult"></div>
            </div>

            <!-- Platform-Specific Configuration -->
            <div class="auth-section" id="platformConfigSection">
                <h2>⚙️ {{PLATFORM_NAME}} API Configuration</h2>
                <div class="warning">
                    <strong>Alternative Method:</strong> Configure {{PLATFORM_NAME}} API directly for advanced features.
                </div>
                
                <!-- Platform-specific form fields will be inserted here -->
                <div id="platformConfigForm">
                    <!-- This will be populated by platform-specific JavaScript -->
                </div>
                
                <button class="btn" onclick="savePlatformConfig()">Save Configuration</button>
                <button class="btn btn-secondary" onclick="testPlatformConnection()">Test Connection</button>
                
                <div id="platformConfigResult"></div>
            </div>

            <!-- Testing Section -->
            <div class="grid">
                <div class="card">
                    <h3>🧪 Test Messaging</h3>
                    <div class="form-group">
                        <label for="testRecipient">Test Recipient:</label>
                        <input type="text" id="testRecipient" placeholder="Recipient ID or phone number">
                    </div>
                    <div class="form-group">
                        <label for="testMessage">Test Message:</label>
                        <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from the unified messaging system."></textarea>
                    </div>
                    <button class="btn" onclick="sendTestMessage()">Send Test Message</button>
                    <div id="testResult"></div>
                </div>

                <div class="card">
                    <h3>📊 Account Information</h3>
                    <button class="btn" onclick="getAccountInfo()">Get Account Info</button>
                    <div id="accountInfoResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkConnectionStatus, 30000);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkConnectionStatus();
            initializePlatformSpecificForm();
        });

        // Base authentication functions (to be extended by platform-specific scripts)
        function checkConnectionStatus() {
            // This will be implemented by platform-specific scripts
            console.log('Checking connection status...');
        }

        function connectUnipile() {
            // This will be implemented by platform-specific scripts
            console.log('Connecting via Unipile...');
        }

        function testUnipileConnection() {
            // This will be implemented by platform-specific scripts
            console.log('Testing Unipile connection...');
        }

        function generateQRCode() {
            // This will be implemented by platform-specific scripts
            console.log('Generating QR code...');
        }

        function refreshQRCode() {
            // This will be implemented by platform-specific scripts
            console.log('Refreshing QR code...');
        }

        function savePlatformConfig() {
            // This will be implemented by platform-specific scripts
            console.log('Saving platform configuration...');
        }

        function testPlatformConnection() {
            // This will be implemented by platform-specific scripts
            console.log('Testing platform connection...');
        }

        function sendTestMessage() {
            // This will be implemented by platform-specific scripts
            console.log('Sending test message...');
        }

        function getAccountInfo() {
            // This will be implemented by platform-specific scripts
            console.log('Getting account info...');
        }

        function initializePlatformSpecificForm() {
            // This will be implemented by platform-specific scripts
            console.log('Initializing platform-specific form...');
        }

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            element.classList.add('fade-in');
        }

        function showLoading(elementId, message = 'Loading...') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="info"><span class="loading-spinner"></span> ${message}</div>`;
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status-indicator status-${status}`;
            statusElement.innerHTML = `<span class="status-dot ${status}"></span> ${message}`;
        }
    </script>

    <!-- Platform-specific JavaScript will be included here -->
    <script src="{{PLATFORM_NAME}}_auth_specific.js"></script>
</body>
</html>
