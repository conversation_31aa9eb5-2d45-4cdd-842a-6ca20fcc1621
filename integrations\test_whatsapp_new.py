"""
Test script for the new WhatsApp integration using Unipile API
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'whatsapp_integration'))

from whatsapp_integration.whatsapp_api import WhatsAppMessaging
import json

def test_whatsapp_integration():
    """Test the new WhatsApp integration"""
    print("🚀 Testing WhatsApp Integration with Unipile API")
    print("=" * 60)
    
    # Initialize WhatsApp messaging
    try:
        whatsapp = WhatsAppMessaging()
        print("✅ WhatsApp messaging client initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize WhatsApp client: {e}")
        return False
    
    # Test 1: Check authentication status
    print("\n🔍 Test 1: Checking authentication status...")
    try:
        status = whatsapp.check_authentication_status()
        print(f"📊 Status: {json.dumps(status, indent=2)}")
        
        if status.get("status") == "connected":
            print(f"✅ WhatsApp is connected: {status.get('phone_number')}")
            is_connected = True
        else:
            print(f"⚠️ WhatsApp not connected: {status.get('message', 'Unknown status')}")
            is_connected = False
            
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        is_connected = False
    
    # Test 2: Get account info (if connected)
    if is_connected:
        print("\n🔍 Test 2: Getting account information...")
        try:
            account_info = whatsapp.get_account_info()
            print(f"📱 Account Info: {json.dumps(account_info, indent=2)}")
        except Exception as e:
            print(f"❌ Failed to get account info: {e}")
    
    # Test 3: Test QR code generation (if not connected)
    if not is_connected:
        print("\n🔍 Test 3: Testing QR code generation...")
        try:
            qr_result = whatsapp.authenticate_account()
            print(f"📷 QR Code Result: {json.dumps(qr_result, indent=2)}")
            
            if qr_result.get("success"):
                print("✅ QR code generated successfully")
                print("📱 Next step: Scan the QR code with your WhatsApp mobile app")
            else:
                print(f"❌ QR code generation failed: {qr_result.get('error')}")
                
        except Exception as e:
            print(f"❌ QR code generation error: {e}")
    
    # Test 4: Test messaging (only if connected)
    if is_connected:
        print("\n🔍 Test 4: Testing message sending capability...")
        print("📝 Note: This will prepare a test message but not actually send it")
        
        try:
            # Test message preparation (don't actually send)
            test_phone = "+**********"  # Dummy number
            test_message = "This is a test message from WhatsApp via Unipile API"
            
            print(f"📱 Test phone: {test_phone}")
            print(f"💬 Test message: {test_message}")
            print("✅ Message sending capability is ready")
            
            # Uncomment the line below to actually send a test message
            # result = whatsapp.send_message(test_phone, test_message)
            # print(f"📤 Send result: {result}")
            
        except Exception as e:
            print(f"❌ Message preparation error: {e}")
    
    # Test 5: Test bulk messaging capability
    if is_connected:
        print("\n🔍 Test 5: Testing bulk messaging capability...")
        try:
            recipients = ["+**********", "+0987654321"]  # Dummy numbers
            message = "Bulk test message"
            
            print(f"📱 Recipients: {recipients}")
            print(f"💬 Message: {message}")
            print("✅ Bulk messaging capability is ready")
            
            # Uncomment the line below to actually send bulk messages
            # results = whatsapp.send_bulk_messages(recipients, message, delay=2.0)
            # print(f"📤 Bulk results: {results}")
            
        except Exception as e:
            print(f"❌ Bulk messaging preparation error: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"  🔗 Client Initialization: ✅ Success")
    print(f"  📊 Authentication Status: {'✅ Connected' if is_connected else '⚠️ Not Connected'}")
    
    if is_connected:
        print(f"  📱 Account Info: ✅ Available")
        print(f"  💬 Messaging: ✅ Ready")
        print(f"  📤 Bulk Messaging: ✅ Ready")
        print("\n🎯 WhatsApp integration is fully functional!")
    else:
        print(f"  📷 QR Code Generation: ✅ Available")
        print("\n⚠️ WhatsApp not connected. Use the HTML interface to scan QR code.")
    
    print("\n💡 Next Steps:")
    if not is_connected:
        print("  1. Open whatsapp_integration/whatsapp_auth.html in your browser")
        print("  2. Click 'Generate QR Code'")
        print("  3. Scan the QR code with your WhatsApp mobile app")
        print("  4. Wait for connection to be established")
        print("  5. Test messaging functionality")
    else:
        print("  1. Use the send_message() method to send individual messages")
        print("  2. Use send_bulk_messages() for bulk messaging")
        print("  3. Monitor connection status with check_authentication_status()")
        print("  4. Use the HTML interface for easy testing and management")
    
    return True

def test_unipile_client():
    """Test the underlying Unipile client"""
    print("\n🔍 Testing Unipile Client...")
    
    try:
        from whatsapp_integration.whatsapp_api import UnipileClient
        
        client = UnipileClient("RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI=")
        
        # Test accounts endpoint
        accounts = client.make_request("GET", "accounts")
        print(f"📊 Unipile Accounts: {json.dumps(accounts, indent=2)}")
        
        if "error" not in accounts:
            print("✅ Unipile client is working correctly")
            return True
        else:
            print(f"❌ Unipile client error: {accounts['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Unipile client test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 WhatsApp Integration Test Suite")
    print("=" * 60)
    
    # Test Unipile client first
    unipile_ok = test_unipile_client()
    
    if unipile_ok:
        # Test WhatsApp integration
        test_whatsapp_integration()
    else:
        print("❌ Unipile client test failed. Cannot proceed with WhatsApp tests.")
        print("\n🔧 Troubleshooting:")
        print("  1. Check your internet connection")
        print("  2. Verify the Unipile API key is correct")
        print("  3. Ensure the Unipile API endpoint is accessible")
