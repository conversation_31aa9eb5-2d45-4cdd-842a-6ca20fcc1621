"""
Flask server for Telegram integration
Provides REST API endpoints for the Telegram authentication HTML interface
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path to import telegram_api
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from telegram_api import TelegramMessaging

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global Telegram instance
telegram_client = None

def get_telegram_client():
    """Get or create Telegram client instance"""
    global telegram_client
    if telegram_client is None:
        telegram_client = TelegramMessaging()
    return telegram_client

@app.route('/')
def index():
    """Serve the Telegram authentication HTML page"""
    return send_from_directory('.', 'telegram_auth.html')

@app.route('/api/telegram/config', methods=['POST'])
def save_config():
    """Save Telegram bot configuration"""
    try:
        data = request.get_json()
        bot_token = data.get('bot_token')
        webhook_url = data.get('webhook_url')
        
        if not bot_token:
            return jsonify({"error": "Bot token is required"}), 400
        
        client = get_telegram_client()
        result = client.update_config(bot_token=bot_token, webhook_url=webhook_url)
        
        logger.info(f"Config update result: {result}")
        return jsonify({"success": True, "message": "Configuration saved successfully"})
        
    except Exception as e:
        logger.error(f"Config save error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/test', methods=['GET'])
def test_connection():
    """Test Telegram bot connection"""
    try:
        client = get_telegram_client()
        
        if not client.is_configured():
            return jsonify({"error": "Bot not configured"}), 400
        
        bot_info = client.get_me()
        
        if bot_info.get("ok"):
            logger.info(f"Connection test successful")
            return jsonify({"success": True, "data": bot_info["result"]})
        else:
            return jsonify({"error": bot_info.get("description", "Connection failed")}), 400
        
    except Exception as e:
        logger.error(f"Connection test error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/bot-info', methods=['GET'])
def get_bot_info():
    """Get Telegram bot information"""
    try:
        client = get_telegram_client()
        result = client.get_bot_info()
        
        if result.get("success"):
            logger.info(f"Bot info retrieved successfully")
            return jsonify({"success": True, "data": result["bot_info"]})
        else:
            return jsonify({"error": result.get("error", "Failed to get bot info")}), 400
        
    except Exception as e:
        logger.error(f"Get bot info error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/webhook', methods=['POST'])
def setup_webhook():
    """Setup webhook for Telegram bot"""
    try:
        data = request.get_json()
        webhook_url = data.get('webhook_url')
        
        if not webhook_url:
            return jsonify({"error": "Webhook URL is required"}), 400
        
        client = get_telegram_client()
        result = client.set_webhook(webhook_url)
        
        if result.get("ok"):
            logger.info(f"Webhook setup successful")
            return jsonify({"success": True, "message": "Webhook set up successfully"})
        else:
            return jsonify({"error": result.get("description", "Webhook setup failed")}), 400
        
    except Exception as e:
        logger.error(f"Webhook setup error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/webhook-info', methods=['GET'])
def get_webhook_info():
    """Get webhook information"""
    try:
        client = get_telegram_client()
        result = client.get_webhook_info()
        
        if result.get("ok"):
            logger.info(f"Webhook info retrieved successfully")
            return jsonify({"success": True, "data": result["result"]})
        else:
            return jsonify({"error": result.get("description", "Failed to get webhook info")}), 400
        
    except Exception as e:
        logger.error(f"Get webhook info error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/send-test', methods=['POST'])
def send_test_message():
    """Send a test message"""
    try:
        data = request.get_json()
        chat_id = data.get('chat_id')
        message = data.get('message')
        
        if not chat_id or not message:
            return jsonify({"error": "Chat ID and message are required"}), 400
        
        client = get_telegram_client()
        result = client.send_message(chat_id, message)
        
        if result.get("success") or result.get("ok"):
            logger.info(f"Test message sent successfully")
            return jsonify({"success": True, "message": "Test message sent successfully"})
        else:
            return jsonify({"error": result.get("error", result.get("description", "Failed to send message"))}), 400
        
    except Exception as e:
        logger.error(f"Send test message error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/send-bulk', methods=['POST'])
def send_bulk_messages():
    """Send bulk messages"""
    try:
        data = request.get_json()
        recipients = data.get('recipients', [])
        message = data.get('message')
        delay = data.get('delay', 1.0)
        
        if not recipients or not message:
            return jsonify({"error": "Recipients and message are required"}), 400
        
        client = get_telegram_client()
        results = client.send_bulk_messages(recipients, message, delay)
        
        logger.info(f"Bulk message results: {len(results)} messages processed")
        return jsonify({"success": True, "results": results})
        
    except Exception as e:
        logger.error(f"Send bulk messages error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/telegram/updates', methods=['GET'])
def get_updates():
    """Get recent updates"""
    try:
        client = get_telegram_client()
        result = client.get_updates()
        
        if result.get("ok"):
            logger.info(f"Updates retrieved successfully")
            return jsonify({"success": True, "data": result})
        else:
            return jsonify({"error": result.get("description", "Failed to get updates")}), 400
        
    except Exception as e:
        logger.error(f"Get updates error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Telegram Integration Server"
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    print("🚀 Starting Telegram Integration Server...")
    print("🤖 Telegram bot interface will be available at: http://localhost:5001")
    print("🔗 API endpoints:")
    print("   - POST /api/telegram/config - Save bot configuration")
    print("   - GET  /api/telegram/test - Test bot connection")
    print("   - GET  /api/telegram/bot-info - Get bot information")
    print("   - POST /api/telegram/webhook - Setup webhook")
    print("   - GET  /api/telegram/webhook-info - Get webhook info")
    print("   - POST /api/telegram/send-test - Send test message")
    print("   - POST /api/telegram/send-bulk - Send bulk messages")
    print("   - GET  /api/telegram/updates - Get recent updates")
    print("   - GET  /health - Health check")
    print("\n💡 Open http://localhost:5001 in your browser to access the Telegram interface")
    
    # Run the Flask app
    app.run(host='0.0.0.0', port=5001, debug=True)
