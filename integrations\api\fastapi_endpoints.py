"""
Unified Messaging FastAPI Endpoints
FastAPI implementation with automatic OpenAPI documentation
Supports single messages, bulk campaigns, and platform management
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Any, Union
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unified_messaging import UnifiedMessaging, MessageResult
from unipile_config import UnipileConfig, is_unipile_available

# Initialize FastAPI app
app = FastAPI(
    title="Unified Messaging API",
    description="Unified messaging system for all social media platforms using Unipile API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize unified messaging system
try:
    unified_messaging = UnifiedMessaging()
    logger.info("✅ Unified messaging system initialized")
except Exception as e:
    logger.error(f"❌ Failed to initialize unified messaging: {e}")
    unified_messaging = None

# Pydantic models for request/response validation
class MessageRequest(BaseModel):
    platform: str = Field(..., description="Platform name (whatsapp, telegram, facebook, instagram, linkedin, tiktok)")
    recipient: str = Field(..., description="Recipient ID (phone number, username, etc.)")
    message: str = Field(..., description="Message content")
    options: Optional[Dict[str, Any]] = Field(default={}, description="Platform-specific options")

class RecipientData(BaseModel):
    contact: str = Field(..., description="Contact information")
    message: str = Field(..., description="Message content")

class BulkMessageRequest(BaseModel):
    campaign: Dict[str, List[RecipientData]] = Field(..., description="Campaign data organized by platform")
    delay: Optional[float] = Field(default=2.0, description="Delay between messages in seconds")

class TemplateRecipient(BaseModel):
    platform: str = Field(..., description="Platform name")
    contact: str = Field(..., description="Contact information")
    name: Optional[str] = Field(default="", description="Recipient name")

class TemplateMessageRequest(BaseModel):
    template_name: str = Field(..., description="Template name")
    recipients: List[TemplateRecipient] = Field(..., description="List of recipients")
    variables: Optional[Dict[str, Any]] = Field(default={}, description="Template variables")

class MessageResponse(BaseModel):
    success: bool
    platform: str
    recipient: str
    message_id: Optional[str] = None
    method: Optional[str] = None
    error: Optional[str] = None
    timestamp: str

class HealthResponse(BaseModel):
    success: bool
    status: str
    unified_messaging: bool
    unipile_available: bool
    timestamp: str

class PlatformStatusResponse(BaseModel):
    success: bool
    platform: Optional[str] = None
    status: Optional[Dict[str, Any]] = None
    platforms: Optional[Dict[str, Any]] = None
    timestamp: str

# Health check endpoint
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        success=True,
        status="healthy",
        unified_messaging=unified_messaging is not None,
        unipile_available=is_unipile_available(),
        timestamp=datetime.now().isoformat()
    )

# Single message sending endpoint
@app.post("/api/messaging/send", response_model=MessageResponse)
async def send_message(request: MessageRequest):
    """
    Send single message to specified platform
    
    - **platform**: Platform name (whatsapp, telegram, facebook, instagram, linkedin, tiktok)
    - **recipient**: Recipient ID (phone number, username, etc.)
    - **message**: Message content
    - **options**: Platform-specific options (optional)
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Send message
        result = unified_messaging.send_message_by_platform(
            request.platform, 
            request.recipient, 
            request.message, 
            **request.options
        )

        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)

        return MessageResponse(
            success=result.success,
            platform=result.platform,
            recipient=result.recipient,
            message_id=result.message_id,
            method=result.method,
            error=result.error,
            timestamp=result.timestamp
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in send_message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk message sending endpoint
@app.post("/api/messaging/bulk")
async def send_bulk_messages(request: BulkMessageRequest, background_tasks: BackgroundTasks):
    """
    Send bulk messages across multiple platforms
    
    - **campaign**: Dictionary with platform names as keys and lists of recipient data as values
    - **delay**: Delay between messages in seconds (default: 2.0)
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Convert Pydantic models to dictionaries
        campaign_data = {}
        for platform, recipients in request.campaign.items():
            campaign_data[platform] = [
                {"contact": r.contact, "message": r.message}
                for r in recipients
            ]

        # Send bulk campaign
        results = unified_messaging.send_bulk_campaign(campaign_data, request.delay)

        # Calculate summary
        total_messages = sum(len(platform_results) for platform_results in results.values())
        successful_messages = sum(
            1 for platform_results in results.values() 
            for result in platform_results if result.success
        )

        # Format response
        response = {
            "success": True,
            "campaign_summary": {
                "total_messages": total_messages,
                "successful": successful_messages,
                "failed": total_messages - successful_messages,
                "success_rate": (successful_messages / total_messages * 100) if total_messages > 0 else 0
            },
            "results": {},
            "timestamp": datetime.now().isoformat()
        }

        # Add detailed results
        for platform, platform_results in results.items():
            response["results"][platform] = [
                {
                    "success": result.success,
                    "recipient": result.recipient,
                    "message_id": result.message_id,
                    "error": result.error,
                    "method": result.method,
                    "timestamp": result.timestamp
                }
                for result in platform_results
            ]

        return response

    except Exception as e:
        logger.error(f"Error in send_bulk_messages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Platform status endpoint
@app.get("/api/messaging/status/{platform}", response_model=PlatformStatusResponse)
async def get_platform_status(platform: str):
    """
    Check if platform is connected and ready
    
    - **platform**: Platform name or 'all' for all platforms
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Get all platform status
        all_status = unified_messaging.get_platform_status()

        if platform.lower() == "all":
            return PlatformStatusResponse(
                success=True,
                platforms=all_status,
                timestamp=datetime.now().isoformat()
            )

        # Get specific platform status
        platform_status = all_status.get(platform.lower())
        if platform_status is None:
            raise HTTPException(status_code=404, detail=f"Platform '{platform}' not supported")

        return PlatformStatusResponse(
            success=True,
            platform=platform.lower(),
            status=platform_status,
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_platform_status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all platforms status
@app.get("/api/messaging/status", response_model=PlatformStatusResponse)
async def get_all_platforms_status():
    """Get status of all platforms"""
    return await get_platform_status("all")

# Message analytics endpoint
@app.get("/api/messaging/analytics")
async def get_message_analytics():
    """Get message analytics and statistics"""
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        analytics = unified_messaging.get_message_analytics()
        
        return {
            "success": True,
            "analytics": analytics,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in get_message_analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Template message endpoint
@app.post("/api/messaging/template")
async def send_template_message(request: TemplateMessageRequest):
    """
    Send template message to multiple recipients
    
    - **template_name**: Name of the template to use
    - **recipients**: List of recipients with platform and contact info
    - **variables**: Variables to substitute in the template
    """
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        # Load template from config
        config = unified_messaging.config
        templates = config.get("message_templates", {})
        
        if request.template_name not in templates:
            raise HTTPException(status_code=404, detail=f"Template '{request.template_name}' not found")

        template_data = templates[request.template_name]
        message_template = template_data.get("text", template_data) if isinstance(template_data, dict) else template_data

        # Send messages to recipients
        results = []
        for recipient in request.recipients:
            # Format message with variables and recipient data
            message_vars = {**request.variables, **recipient.dict()}
            try:
                message = message_template.format(**message_vars)
            except KeyError as e:
                results.append({
                    "success": False,
                    "error": f"Missing template variable: {e}",
                    "recipient": recipient.dict()
                })
                continue

            # Send message
            result = unified_messaging.send_message_by_platform(
                recipient.platform, 
                recipient.contact, 
                message
            )
            results.append({
                "success": result.success,
                "platform": result.platform,
                "recipient": result.recipient,
                "message_id": result.message_id,
                "error": result.error,
                "method": result.method,
                "timestamp": result.timestamp
            })

        # Calculate summary
        total = len(results)
        successful = sum(1 for r in results if r.get("success"))

        return {
            "success": True,
            "template_name": request.template_name,
            "summary": {
                "total": total,
                "successful": successful,
                "failed": total - successful
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in send_template_message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Configuration endpoints
@app.get("/api/config/templates")
async def get_templates():
    """Get available message templates"""
    if not unified_messaging:
        raise HTTPException(status_code=503, detail="Unified messaging system not available")

    try:
        config = unified_messaging.config
        templates = config.get("message_templates", {})
        
        return {
            "success": True,
            "templates": templates,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in get_templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config/platforms")
async def get_supported_platforms():
    """Get list of supported platforms"""
    return {
        "success": True,
        "platforms": [
            {
                "name": "whatsapp",
                "display_name": "WhatsApp",
                "supports_media": True,
                "auth_method": "qr_code"
            },
            {
                "name": "telegram",
                "display_name": "Telegram",
                "supports_media": True,
                "auth_method": "bot_token"
            },
            {
                "name": "facebook",
                "display_name": "Facebook Messenger",
                "supports_media": True,
                "auth_method": "oauth"
            },
            {
                "name": "instagram",
                "display_name": "Instagram",
                "supports_media": True,
                "auth_method": "oauth"
            },
            {
                "name": "linkedin",
                "display_name": "LinkedIn",
                "supports_media": False,
                "auth_method": "oauth"
            },
            {
                "name": "tiktok",
                "display_name": "TikTok",
                "supports_media": False,
                "auth_method": "oauth"
            }
        ],
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
