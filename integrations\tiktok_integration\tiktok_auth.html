<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok for Business API Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #ff0050, #ff4081, #ff6ec7);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        h1 {
            background: linear-gradient(135deg, #ff0050, #ff4081);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: bold;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #ff0050;
            background-color: #f9f9f9;
            border-radius: 0 15px 15px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            border-color: #ff0050;
            outline: none;
        }
        button {
            background: linear-gradient(135deg, #ff0050, #ff4081);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #ff0050;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .limitation {
            background-color: #ffeaa7;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #d63031;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 TikTok for Business API Setup</h1>
        
        <div class="limitation">
            <strong>⚠️ Important Limitation:</strong> TikTok does not have traditional direct messaging APIs like other platforms. 
            This integration focuses on content management, analytics, and engagement through comments and video responses.
        </div>

        <div class="step">
            <h3>Step 1: Create TikTok for Business Account</h3>
            <p>1. Go to <a href="https://business.tiktok.com/" target="_blank">TikTok for Business</a></p>
            <p>2. Sign up or log in with your TikTok account</p>
            <p>3. Convert your personal account to a Business account if needed</p>
            <p>4. Complete your business profile setup</p>
        </div>

        <div class="step">
            <h3>Step 2: Apply for TikTok Developer Access</h3>
            <p>1. Visit <a href="https://developers.tiktok.com/" target="_blank">TikTok Developers</a></p>
            <p>2. Apply for developer access (approval required)</p>
            <p>3. Create a new app in the developer console</p>
            <p>4. Configure your app with appropriate scopes and permissions</p>
        </div>

        <div class="step">
            <h3>Step 3: Get Your App Credentials</h3>
            
            <form id="tiktokConfigForm">
                <div class="form-group">
                    <label for="clientKey">Client Key:</label>
                    <input type="text" id="clientKey" name="clientKey" 
                           placeholder="Your TikTok app client key">
                </div>
                
                <div class="form-group">
                    <label for="clientSecret">Client Secret:</label>
                    <input type="password" id="clientSecret" name="clientSecret" 
                           placeholder="Your TikTok app client secret">
                </div>
                
                <div class="form-group">
                    <label for="accessToken">Access Token:</label>
                    <input type="password" id="accessToken" name="accessToken" 
                           placeholder="Your access token (obtained through OAuth)">
                </div>
                
                <div class="form-group">
                    <label for="refreshToken">Refresh Token:</label>
                    <input type="password" id="refreshToken" name="refreshToken" 
                           placeholder="Your refresh token">
                </div>
                
                <div class="form-group">
                    <label for="openId">Open ID:</label>
                    <input type="text" id="openId" name="openId" 
                           placeholder="Your TikTok Open ID">
                </div>
                
                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>
            
            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: OAuth Authorization Flow</h3>
            <p>To get access tokens, you need to implement OAuth 2.0 flow:</p>
            <div class="code">
                Authorization URL: https://www.tiktok.com/auth/authorize/<br>
                Token URL: https://open-api.tiktok.com/oauth/access_token/
            </div>
            <p>Required scopes for business features:</p>
            <ul>
                <li><strong>user.info.basic:</strong> Basic user information</li>
                <li><strong>video.list:</strong> Access to user's videos</li>
                <li><strong>video.upload:</strong> Upload videos</li>
                <li><strong>video.publish:</strong> Publish videos</li>
            </ul>
            <button type="button" onclick="initiateOAuth()">Start OAuth Flow</button>
            <div id="oauthResult"></div>
        </div>

        <div class="step">
            <h3>Step 5: Account Information</h3>
            <p>Once configured, get your TikTok account information:</p>
            <button type="button" onclick="getUserInfo()">Get User Info</button>
            <div id="userInfoResult"></div>
        </div>

        <div class="step">
            <h3>Step 6: Content Management</h3>
            <p>TikTok API allows you to manage your content:</p>
            <div class="form-group">
                <label for="videoPath">Video File Path:</label>
                <input type="text" id="videoPath" placeholder="/path/to/your/video.mp4">
            </div>
            <div class="form-group">
                <label for="videoDescription">Video Description:</label>
                <textarea id="videoDescription" rows="3" placeholder="Your video description with #hashtags"></textarea>
            </div>
            <button type="button" onclick="uploadVideo()">Upload Video</button>
            <button type="button" onclick="getVideoList()">Get My Videos</button>
            <div id="contentResult"></div>
        </div>

        <div class="step">
            <h3>Step 7: Engagement Features</h3>
            <p>Engage with your audience through comments and analytics:</p>
            <div class="form-group">
                <label for="videoId">Video ID:</label>
                <input type="text" id="videoId" placeholder="Video ID for comments/analytics">
            </div>
            <button type="button" onclick="getVideoComments()">Get Comments</button>
            <button type="button" onclick="getVideoAnalytics()">Get Analytics</button>
            <div id="engagementResult"></div>
        </div>

        <div class="step">
            <h3>Step 8: Comment Replies</h3>
            <p>Reply to comments on your videos:</p>
            <div class="form-group">
                <label for="commentId">Comment ID:</label>
                <input type="text" id="commentId" placeholder="Comment ID to reply to">
            </div>
            <div class="form-group">
                <label for="replyText">Reply Text:</label>
                <textarea id="replyText" rows="2" placeholder="Your reply to the comment (max 150 chars)"></textarea>
            </div>
            <button type="button" onclick="replyToComment()">Reply to Comment</button>
            <div id="replyResult"></div>
        </div>

        <div class="step">
            <h3>Step 9: Alternative Engagement Strategies</h3>
            <p>Since TikTok lacks direct messaging, consider these engagement methods:</p>
            <ul>
                <li><strong>Video Responses:</strong> Create video responses to user comments</li>
                <li><strong>Duets & Stitches:</strong> Engage with user content through duets</li>
                <li><strong>Live Streaming:</strong> Interact with followers in real-time</li>
                <li><strong>Comment Engagement:</strong> Actively respond to comments</li>
                <li><strong>Hashtag Challenges:</strong> Create branded hashtag challenges</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 10: Rate Limits & Best Practices</h3>
            <ul>
                <li><strong>Rate Limits:</strong> 100 requests per minute, 1000 per hour</li>
                <li><strong>Content Guidelines:</strong> Follow TikTok's community guidelines</li>
                <li><strong>Authentic Engagement:</strong> Focus on genuine interactions</li>
                <li><strong>Trending Content:</strong> Stay updated with TikTok trends</li>
                <li><strong>Analytics:</strong> Use analytics to optimize content strategy</li>
            </ul>
        </div>
    </div>

    <script>
        function saveConfig() {
            const config = {
                client_key: document.getElementById('clientKey').value,
                client_secret: document.getElementById('clientSecret').value,
                access_token: document.getElementById('accessToken').value,
                refresh_token: document.getElementById('refreshToken').value,
                open_id: document.getElementById('openId').value
            };
            
            fetch('/api/tiktok/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/tiktok/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function initiateOAuth() {
            const clientKey = document.getElementById('clientKey').value;
            if (!clientKey) {
                document.getElementById('oauthResult').innerHTML = 
                    '<div class="error">❌ Please enter Client Key first</div>';
                return;
            }
            
            const authUrl = `https://www.tiktok.com/auth/authorize/?client_key=${clientKey}&scope=user.info.basic,video.list,video.upload&response_type=code&redirect_uri=YOUR_REDIRECT_URI`;
            document.getElementById('oauthResult').innerHTML = 
                `<div class="info">Open this URL to authorize: <a href="${authUrl}" target="_blank">Authorize App</a></div>`;
        }
        
        function getUserInfo() {
            fetch('/api/tiktok/user-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('userInfoResult');
                if (data.success) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>User Information:</strong><br>
                            Display Name: ${info.display_name}<br>
                            Open ID: ${info.open_id}<br>
                            Followers: ${info.follower_count}<br>
                            Following: ${info.following_count}<br>
                            Likes: ${info.likes_count}<br>
                            Videos: ${info.video_count}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get user info: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('userInfoResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function uploadVideo() {
            const videoPath = document.getElementById('videoPath').value;
            const description = document.getElementById('videoDescription').value;
            
            if (!videoPath) {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Please enter video file path</div>';
                return;
            }
            
            fetch('/api/tiktok/upload-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_path: videoPath,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Video uploaded successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to upload video: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoList() {
            fetch('/api/tiktok/videos')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('contentResult');
                if (data.success && data.data.videos) {
                    const videos = data.data.videos;
                    let html = '<div class="success"><strong>Your Videos:</strong><br>';
                    videos.slice(0, 5).forEach(video => {
                        html += `Title: ${video.title}, Views: ${video.view_count}, Likes: ${video.like_count}<br>`;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get videos: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('contentResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoComments() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }
            
            fetch(`/api/tiktok/comments/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('engagementResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Comments retrieved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get comments: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function getVideoAnalytics() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Please enter video ID</div>';
                return;
            }
            
            fetch(`/api/tiktok/analytics/${videoId}`)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('engagementResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Analytics retrieved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get analytics: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('engagementResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function replyToComment() {
            const videoId = document.getElementById('videoId').value;
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('replyText').value;
            
            if (!videoId || !commentId || !replyText) {
                document.getElementById('replyResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/tiktok/reply-comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    comment_id: commentId,
                    reply_text: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('replyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Reply posted successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to post reply: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('replyResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
