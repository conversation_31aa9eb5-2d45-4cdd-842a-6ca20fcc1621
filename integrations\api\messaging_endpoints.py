"""
Unified Messaging API Endpoints
Flask/FastAPI endpoints for the unified messaging system
Supports single messages, bulk campaigns, and platform management
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unified_messaging import UnifiedMessaging, MessageResult
from unipile_config import UnipileConfig, is_unipile_available

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize unified messaging system
try:
    unified_messaging = UnifiedMessaging()
    logger.info("✅ Unified messaging system initialized")
except Exception as e:
    logger.error(f"❌ Failed to initialize unified messaging: {e}")
    unified_messaging = None

# Error handler
@app.errorhandler(Exception)
def handle_exception(e):
    logger.error(f"Unhandled exception: {e}")
    return jsonify({
        "success": False,
        "error": str(e),
        "timestamp": datetime.now().isoformat()
    }), 500

# Health check endpoint
@app.route("/api/health", methods=["GET"])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "success": True,
        "status": "healthy",
        "unified_messaging": unified_messaging is not None,
        "unipile_available": is_unipile_available(),
        "timestamp": datetime.now().isoformat()
    })

# Single message sending endpoint
@app.route("/api/messaging/send", methods=["POST"])
def send_message():
    """
    Send single message
    Body: {
        "platform": "whatsapp",
        "recipient": "+**********", 
        "message": "Hello!",
        "options": {}  # Optional platform-specific options
    }
    """
    try:
        if not unified_messaging:
            return jsonify({
                "success": False,
                "error": "Unified messaging system not available"
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        required_fields = ["platform", "recipient", "message"]
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return jsonify({
                "success": False,
                "error": f"Missing required fields: {', '.join(missing_fields)}"
            }), 400

        platform = data["platform"]
        recipient = data["recipient"]
        message = data["message"]
        options = data.get("options", {})

        # Send message
        result = unified_messaging.send_message_by_platform(
            platform, recipient, message, **options
        )

        # Format response
        response = {
            "success": result.success,
            "platform": result.platform,
            "recipient": result.recipient,
            "message_id": result.message_id,
            "method": result.method,
            "timestamp": result.timestamp
        }

        if not result.success:
            response["error"] = result.error

        status_code = 200 if result.success else 400
        return jsonify(response), status_code

    except Exception as e:
        logger.error(f"Error in send_message: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# Bulk message sending endpoint
@app.route("/api/messaging/bulk", methods=["POST"])
def send_bulk_messages():
    """
    Send bulk messages
    Body: {
        "campaign": {
            "whatsapp": [
                {"contact": "+**********", "message": "Hello!"},
                {"contact": "+0987654321", "message": "Hi there!"}
            ],
            "telegram": [
                {"contact": "@username", "message": "Hey!"}
            ]
        },
        "delay": 2.0  # Optional delay between messages
    }
    """
    try:
        if not unified_messaging:
            return jsonify({
                "success": False,
                "error": "Unified messaging system not available"
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        campaign_data = data.get("campaign")
        if not campaign_data:
            return jsonify({
                "success": False,
                "error": "No campaign data provided"
            }), 400

        delay = data.get("delay", 2.0)

        # Send bulk campaign
        results = unified_messaging.send_bulk_campaign(campaign_data, delay)

        # Format response
        total_messages = sum(len(platform_results) for platform_results in results.values())
        successful_messages = sum(
            1 for platform_results in results.values() 
            for result in platform_results if result.success
        )

        response = {
            "success": True,
            "campaign_summary": {
                "total_messages": total_messages,
                "successful": successful_messages,
                "failed": total_messages - successful_messages,
                "success_rate": (successful_messages / total_messages * 100) if total_messages > 0 else 0
            },
            "results": {},
            "timestamp": datetime.now().isoformat()
        }

        # Add detailed results
        for platform, platform_results in results.items():
            response["results"][platform] = [
                {
                    "success": result.success,
                    "recipient": result.recipient,
                    "message_id": result.message_id,
                    "error": result.error,
                    "method": result.method,
                    "timestamp": result.timestamp
                }
                for result in platform_results
            ]

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error in send_bulk_messages: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# Platform status endpoint
@app.route("/api/messaging/status/<platform>", methods=["GET"])
def get_platform_status(platform):
    """Check if platform is connected and ready"""
    try:
        if not unified_messaging:
            return jsonify({
                "success": False,
                "error": "Unified messaging system not available"
            }), 503

        # Get all platform status
        all_status = unified_messaging.get_platform_status()

        if platform.lower() == "all":
            return jsonify({
                "success": True,
                "platforms": all_status,
                "timestamp": datetime.now().isoformat()
            })

        # Get specific platform status
        platform_status = all_status.get(platform.lower())
        if platform_status is None:
            return jsonify({
                "success": False,
                "error": f"Platform '{platform}' not supported"
            }), 404

        return jsonify({
            "success": True,
            "platform": platform.lower(),
            "status": platform_status,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in get_platform_status: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# Get all platforms status
@app.route("/api/messaging/status", methods=["GET"])
def get_all_platforms_status():
    """Get status of all platforms"""
    return get_platform_status("all")

# Message analytics endpoint
@app.route("/api/messaging/analytics", methods=["GET"])
def get_message_analytics():
    """Get message analytics"""
    try:
        if not unified_messaging:
            return jsonify({
                "success": False,
                "error": "Unified messaging system not available"
            }), 503

        analytics = unified_messaging.get_message_analytics()
        
        return jsonify({
            "success": True,
            "analytics": analytics,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in get_message_analytics: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# Template message endpoint
@app.route("/api/messaging/template", methods=["POST"])
def send_template_message():
    """
    Send template message
    Body: {
        "template_name": "welcome",
        "recipients": [
            {"platform": "whatsapp", "contact": "+**********", "name": "John"},
            {"platform": "telegram", "contact": "@username", "name": "Jane"}
        ],
        "variables": {"company": "ACME Corp"}
    }
    """
    try:
        if not unified_messaging:
            return jsonify({
                "success": False,
                "error": "Unified messaging system not available"
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        template_name = data.get("template_name")
        recipients = data.get("recipients", [])
        variables = data.get("variables", {})

        if not template_name:
            return jsonify({
                "success": False,
                "error": "Template name is required"
            }), 400

        if not recipients:
            return jsonify({
                "success": False,
                "error": "Recipients list is required"
            }), 400

        # Load template from config
        config = unified_messaging.config
        templates = config.get("message_templates", {})
        
        if template_name not in templates:
            return jsonify({
                "success": False,
                "error": f"Template '{template_name}' not found"
            }), 404

        template_data = templates[template_name]
        message_template = template_data.get("text", template_data) if isinstance(template_data, dict) else template_data

        # Send messages to recipients
        results = []
        for recipient in recipients:
            platform = recipient.get("platform")
            contact = recipient.get("contact")
            
            if not platform or not contact:
                results.append({
                    "success": False,
                    "error": "Platform and contact are required for each recipient",
                    "recipient": recipient
                })
                continue

            # Format message with variables and recipient data
            message_vars = {**variables, **recipient}
            try:
                message = message_template.format(**message_vars)
            except KeyError as e:
                results.append({
                    "success": False,
                    "error": f"Missing template variable: {e}",
                    "recipient": recipient
                })
                continue

            # Send message
            result = unified_messaging.send_message_by_platform(platform, contact, message)
            results.append({
                "success": result.success,
                "platform": result.platform,
                "recipient": result.recipient,
                "message_id": result.message_id,
                "error": result.error,
                "method": result.method,
                "timestamp": result.timestamp
            })

        # Calculate summary
        total = len(results)
        successful = sum(1 for r in results if r.get("success"))

        return jsonify({
            "success": True,
            "template_name": template_name,
            "summary": {
                "total": total,
                "successful": successful,
                "failed": total - successful
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in send_template_message: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# Platform-specific configuration endpoints
@app.route("/api/<platform>/config", methods=["POST"])
def update_platform_config(platform):
    """Update platform-specific configuration"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Import platform-specific API classes
        platform_apis = {
            "whatsapp": "whatsapp_integration.whatsapp_api",
            "telegram": "telegram_integration.telegram_api",
            "facebook": "facebook_integration.facebook_api",
            "instagram": "instagram_integration.instagram_api",
            "linkedin": "linkedin_integration.linkedin_api",
            "tiktok": "tiktok_integration.tiktok_api"
        }

        if platform not in platform_apis:
            return jsonify({
                "success": False,
                "error": f"Platform '{platform}' not supported"
            }), 404

        # Update configuration based on platform
        if platform == "whatsapp":
            from whatsapp_integration.whatsapp_api import WhatsAppMessaging
            api = WhatsAppMessaging()
            result = api.update_config(**data)
        elif platform == "telegram":
            from telegram_integration.telegram_api import TelegramMessaging
            api = TelegramMessaging()
            result = api.update_config(**data)
        elif platform == "facebook":
            from facebook_integration.facebook_api import FacebookMessaging
            api = FacebookMessaging()
            result = api.update_config(**data)
        elif platform == "instagram":
            from instagram_integration.instagram_api import InstagramMessaging
            api = InstagramMessaging()
            result = api.update_config(**data)
        elif platform == "linkedin":
            from linkedin_integration.linkedin_api import LinkedInMessaging
            api = LinkedInMessaging()
            result = api.update_config(**data)
        elif platform == "tiktok":
            from tiktok_integration.tiktok_api import TikTokMessaging
            api = TikTokMessaging()
            result = api.update_config(**data)

        return jsonify({
            "success": True,
            "platform": platform,
            "result": result,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error updating {platform} config: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route("/api/<platform>/test", methods=["GET"])
def test_platform_connection(platform):
    """Test platform connection"""
    try:
        if platform not in ["whatsapp", "telegram", "facebook", "instagram", "linkedin", "tiktok"]:
            return jsonify({
                "success": False,
                "error": f"Platform '{platform}' not supported"
            }), 404

        # Test connection based on platform
        if platform == "whatsapp":
            from whatsapp_integration.whatsapp_api import WhatsAppMessaging
            api = WhatsAppMessaging()
            result = api.test_connection()
        elif platform == "telegram":
            from telegram_integration.telegram_api import TelegramMessaging
            api = TelegramMessaging()
            result = api.test_connection()
        elif platform == "facebook":
            from facebook_integration.facebook_api import FacebookMessaging
            api = FacebookMessaging()
            result = api.test_connection()
        elif platform == "instagram":
            from instagram_integration.instagram_api import InstagramMessaging
            api = InstagramMessaging()
            result = api.test_connection()
        elif platform == "linkedin":
            from linkedin_integration.linkedin_api import LinkedInMessaging
            api = LinkedInMessaging()
            result = api.test_connection()
        elif platform == "tiktok":
            from tiktok_integration.tiktok_api import TikTokMessaging
            api = TikTokMessaging()
            result = api.test_connection()

        return jsonify({
            "success": True,
            "platform": platform,
            "test_result": result,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error testing {platform} connection: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

# Unipile-specific endpoints
@app.route("/api/unipile/connect", methods=["POST"])
def connect_unipile():
    """Connect to Unipile API"""
    try:
        data = request.get_json()
        api_key = data.get("api_key")

        if not api_key:
            return jsonify({
                "success": False,
                "error": "API key is required"
            }), 400

        # Update Unipile configuration
        from unipile_config import UnipileConfig
        config = UnipileConfig()
        config.api_key = api_key
        config.save_config()

        # Test connection
        test_result = config.test_connection()

        return jsonify({
            "success": test_result.get("success", False),
            "message": test_result.get("message", ""),
            "error": test_result.get("error"),
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error connecting to Unipile: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route("/api/unipile/accounts", methods=["GET"])
def get_unipile_accounts():
    """Get Unipile connected accounts"""
    try:
        from unipile_config import get_unipile_client
        client = get_unipile_client()
        accounts = client.get_accounts()

        return jsonify({
            "success": True,
            "accounts": accounts,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting Unipile accounts: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

if __name__ == "__main__":
    # Development server
    app.run(debug=True, host="0.0.0.0", port=5000)
