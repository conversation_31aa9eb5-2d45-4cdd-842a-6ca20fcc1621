"""
Instagram Messaging API Integration
Handles authentication and messaging for Instagram Business/Creator accounts
Uses Unipile API as primary method 
Focus on comment replies and story interactions due to Instagram's messaging limitations
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime
import urllib.parse
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_api import UnipileAPI

class InstagramMessaging:
    def __init__(self, config_path: str = "integrations/instagram_integration/config.json", use_unipile: bool = True):
        """Initialize Instagram API client with Unipile"""
        self.config_path = config_path
        self.config = self._load_config()
        self.use_unipile = use_unipile

        # Unipile API setup (primary method)
        self.unipile_client = None
        if use_unipile:
            try:
                self.unipile_client = UnipileAPI()
                self.logger = logging.getLogger(__name__)
                self.logger.info("Unipile API client initialized for Instagram")
            except Exception as e:
                self.logger = logging.getLogger(__name__)
                self.logger.warning(f"Failed to initialize Unipile API: {e}")
                self.use_unipile = False

        self.access_token = self.config.get("access_token")
        self.instagram_account_id = self.config.get("instagram_account_id")
        self.facebook_page_id = self.config.get("facebook_page_id")
        self.api_version = self.config.get("api_version", "v18.0")
        self.base_url = f"{self.config.get('base_url', 'https://graph.facebook.com')}/{self.api_version}"

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 5)

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        if not hasattr(self, 'logger'):
            self.logger = logging.getLogger(__name__)

        # Account connection status
        self.connection_status = {
            "unipile": False,
            "facebook_graph": False
        }
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.access_token:
            return {"error": "Access token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        # Add access token to params
        if not params:
            params = {}
        params["access_token"] = self.access_token
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data, params=params)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

    def authenticate_account(self, account_id: str = None) -> Dict:
        """Authenticate Instagram account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()
            instagram_accounts = [acc for acc in accounts.get("accounts", [])
                                if acc.get("provider") == "instagram"]

            if instagram_accounts:
                self.connection_status["unipile"] = True
                self.logger.info("Instagram account already connected via Unipile")
                return {
                    "success": True,
                    "message": "Instagram account connected",
                    "accounts": instagram_accounts
                }
            else:
                # Return authentication URL or instructions
                return {
                    "success": False,
                    "message": "No Instagram account connected. Please connect via Unipile dashboard.",
                    "auth_required": True
                }
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}

    def send_message(self, recipient_id: str, text: str, **kwargs) -> Dict:
        """Send Instagram message via Unipile (primary) or Facebook Graph API (fallback)"""
        # Try Unipile first
        if self.use_unipile and self.unipile_client:
            try:
                result = self.unipile_client.send_instagram_message(recipient_id, text, **kwargs)
                if "error" not in result:
                    self.logger.info(f"Message sent via Unipile to {recipient_id}")
                    return result
                else:
                    self.logger.warning(f"Unipile failed: {result.get('error')}, trying fallback")
            except Exception as e:
                self.logger.warning(f"Unipile error: {e}, trying fallback")

        # Fallback to Facebook Graph API
        return self.send_direct_message(recipient_id, text, **kwargs)

    def send_bulk_messages(self, recipients: List[str], message: str, delay: float = 3.0) -> List[Dict]:
        """Send message to multiple recipients via Unipile (primary) or Facebook Graph API (fallback)"""
        results = []

        # Try Unipile first for bulk messaging
        if self.use_unipile and self.unipile_client:
            try:
                # Unipile bulk messaging
                for recipient_id in recipients:
                    result = self.send_message(recipient_id, message)
                    results.append({
                        "recipient_id": recipient_id,
                        "result": result,
                        "method": "unipile" if "error" not in result else "fallback",
                        "timestamp": datetime.now().isoformat()
                    })

                    if delay > 0:
                        time.sleep(delay)

                return results
            except Exception as e:
                self.logger.warning(f"Unipile bulk messaging error: {e}, using fallback")

        # Fallback to Facebook Graph API bulk messaging
        for recipient_id in recipients:
            result = self.send_direct_message(recipient_id, message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "method": "facebook_graph",
                "timestamp": datetime.now().isoformat()
            })

            if delay > 0:
                time.sleep(delay)

        return results

    def get_connection_status(self) -> Dict:
        """Get current connection status for both Unipile and Facebook Graph API"""
        status = {
            "unipile": {
                "available": bool(self.unipile_client),
                "connected": False,
                "accounts": []
            },
            "facebook_graph": {
                "available": bool(self.access_token and self.instagram_account_id),
                "connected": False,
                "account_id": self.instagram_account_id
            }
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                instagram_accounts = [acc for acc in accounts.get("accounts", [])
                                    if acc.get("provider") == "instagram"]
                status["unipile"]["connected"] = len(instagram_accounts) > 0
                status["unipile"]["accounts"] = instagram_accounts
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")

        # Check Facebook Graph API connection
        if self.access_token and self.instagram_account_id:
            try:
                account_info = self.get_account_info()
                status["facebook_graph"]["connected"] = "error" not in account_info
            except Exception as e:
                self.logger.error(f"Error checking Facebook Graph API status: {e}")

        return status

    def get_account_info(self) -> Dict:
        """Get Instagram account information"""
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}
        
        params = {
            "fields": "id,username,name,profile_picture_url,followers_count,follows_count,media_count"
        }
        
        return self._make_request("GET", self.instagram_account_id, params=params)
    
    def send_direct_message(self, recipient_id: str, message: str, 
                           quick_replies: List[Dict] = None) -> Dict:
        """Send direct message to Instagram user"""
        if not self.instagram_account_id:
            return {"error": "Instagram account not configured properly"}
        
        message_data = {
            "text": message
        }
        
        if quick_replies:
            message_data["quick_replies"] = quick_replies
        
        data = {
            "recipient": {"id": recipient_id},
            "message": message_data
        }
        
        endpoint = f"{self.instagram_account_id}/messages"
        result = self._make_request("POST", endpoint, data)
        
        if "error" not in result:
            self.logger.info(f"Direct message sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send direct message: {result}")
        
        return result
    
    def send_media_message(self, recipient_id: str, media_url: str, 
                          media_type: str = "image", caption: str = None) -> Dict:
        """Send media message (image/video) to Instagram user"""
        if media_type not in ["image", "video"]:
            return {"error": "Media type must be 'image' or 'video'"}
        
        attachment = {
            "type": media_type,
            "payload": {
                "url": media_url
            }
        }
        
        message_data = {
            "attachment": attachment
        }
        
        data = {
            "recipient": {"id": recipient_id},
            "message": message_data
        }
        
        endpoint = f"{self.instagram_account_id}/messages"
        result = self._make_request("POST", endpoint, data)
        
        # Send caption as separate message if provided
        if caption and "error" not in result:
            self.send_direct_message(recipient_id, caption)
        
        if "error" not in result:
            self.logger.info(f"Media message sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send media message: {result}")
        
        return result
    
    def get_conversations(self, limit: int = 25) -> Dict:
        """Get Instagram conversations"""
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}
        
        params = {
            "fields": "id,participants,updated_time,message_count",
            "limit": limit
        }
        
        endpoint = f"{self.instagram_account_id}/conversations"
        return self._make_request("GET", endpoint, params=params)
    
    def get_messages(self, conversation_id: str, limit: int = 25) -> Dict:
        """Get messages from a conversation"""
        params = {
            "fields": "id,created_time,from,to,message,attachments",
            "limit": limit
        }
        
        endpoint = f"{conversation_id}/messages"
        return self._make_request("GET", endpoint, params=params)
    
    def create_media_post(self, image_url: str, caption: str = None, 
                         location_id: str = None) -> Dict:
        """Create Instagram media post"""
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}
        
        data = {
            "image_url": image_url,
            "caption": caption or "",
            "access_token": self.access_token
        }
        
        if location_id:
            data["location_id"] = location_id
        
        # First, create media object
        endpoint = f"{self.instagram_account_id}/media"
        media_result = self._make_request("POST", endpoint, data)
        
        if "error" in media_result:
            return media_result
        
        # Then publish the media
        creation_id = media_result.get("id")
        if not creation_id:
            return {"error": "Failed to get media creation ID"}
        
        publish_data = {
            "creation_id": creation_id
        }
        
        endpoint = f"{self.instagram_account_id}/media_publish"
        result = self._make_request("POST", endpoint, publish_data)
        
        if "error" not in result:
            self.logger.info("Instagram post created successfully")
        else:
            self.logger.error(f"Failed to publish Instagram post: {result}")
        
        return result
    
    def create_story(self, media_url: str, media_type: str = "image") -> Dict:
        """Create Instagram story"""
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}
        
        if media_type not in ["image", "video"]:
            return {"error": "Media type must be 'image' or 'video'"}
        
        data = {
            f"{media_type}_url": media_url,
            "media_type": "STORIES"
        }
        
        # Create media object for story
        endpoint = f"{self.instagram_account_id}/media"
        media_result = self._make_request("POST", endpoint, data)
        
        if "error" in media_result:
            return media_result
        
        # Publish the story
        creation_id = media_result.get("id")
        if not creation_id:
            return {"error": "Failed to get story creation ID"}
        
        publish_data = {
            "creation_id": creation_id
        }
        
        endpoint = f"{self.instagram_account_id}/media_publish"
        result = self._make_request("POST", endpoint, publish_data)
        
        if "error" not in result:
            self.logger.info("Instagram story created successfully")
        else:
            self.logger.error(f"Failed to publish Instagram story: {result}")
        
        return result

    def reply_to_comment(self, comment_id: str, reply_text: str) -> Dict:
        """Reply to a comment on Instagram post (recommended for engagement)"""
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}

        data = {
            "message": reply_text
        }

        endpoint = f"{comment_id}/replies"
        result = self._make_request("POST", endpoint, data)

        if "error" not in result:
            self.logger.info(f"Comment reply sent successfully to {comment_id}")
        else:
            self.logger.error(f"Failed to reply to comment: {result}")

        return result

    def get_post_comments(self, media_id: str, limit: int = 25) -> Dict:
        """Get comments on a specific Instagram post"""
        params = {
            "fields": "id,text,username,timestamp,like_count,replies",
            "limit": limit
        }

        endpoint = f"{media_id}/comments"
        return self._make_request("GET", endpoint, params=params)

    def get_story_mentions(self, limit: int = 25) -> Dict:
        """Get story mentions for the Instagram account"""
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}

        params = {
            "fields": "id,media_type,media_url,timestamp,username",
            "limit": limit
        }

        endpoint = f"{self.instagram_account_id}/story_insights"
        return self._make_request("GET", endpoint, params=params)

    def respond_to_story_mention(self, story_id: str, response_text: str) -> Dict:
        """Respond to a story mention (creates a direct message)"""
        # Note: This creates a DM, so same restrictions apply
        if not self.instagram_account_id:
            return {"error": "Instagram account ID not configured"}

        # Get story details first
        story_details = self._make_request("GET", story_id, params={"fields": "from"})

        if "error" in story_details:
            return story_details

        user_id = story_details.get("from", {}).get("id")
        if not user_id:
            return {"error": "Could not get user ID from story mention"}

        # Send DM response
        return self.send_direct_message(user_id, response_text)

    def get_hashtag_mentions(self, hashtag: str, limit: int = 25) -> Dict:
        """Get recent posts mentioning a specific hashtag"""
        # Note: This requires additional permissions and may be limited
        params = {
            "fields": "id,media_type,media_url,permalink,timestamp,caption",
            "limit": limit
        }

        # Use hashtag search endpoint
        endpoint = f"ig_hashtag_search?user_id={self.instagram_account_id}&q={hashtag}"
        return self._make_request("GET", endpoint, params=params)

    def get_media_insights(self, media_id: str) -> Dict:
        """Get insights for Instagram media"""
        params = {
            "metric": "impressions,reach,likes,comments,shares,saved"
        }
        
        endpoint = f"{media_id}/insights"
        return self._make_request("GET", endpoint, params=params)
    
    def send_bulk_messages(self, recipients: List[str], message: str, 
                          delay: float = 3.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        results = []
        
        for recipient_id in recipients:
            result = self.send_direct_message(recipient_id, message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, recipient_id: str, template_name: str, 
                            **kwargs) -> Dict:
        """Send predefined template message"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}
        
        message = templates[template_name].format(**kwargs)
        
        # Add quick replies if available
        quick_replies = self.config.get("quick_replies", [])
        
        return self.send_direct_message(recipient_id, message, quick_replies)
    
    def is_configured(self) -> bool:
        """Check if Instagram API is properly configured"""
        return bool(self.access_token and self.instagram_account_id)
    
    def update_config(self, access_token: str = None, instagram_account_id: str = None,
                     facebook_page_id: str = None):
        """Update configuration"""
        if access_token:
            self.config["access_token"] = access_token
            self.access_token = access_token
        
        if instagram_account_id:
            self.config["instagram_account_id"] = instagram_account_id
            self.instagram_account_id = instagram_account_id
        
        if facebook_page_id:
            self.config["facebook_page_id"] = facebook_page_id
            self.facebook_page_id = facebook_page_id
        
        self._save_config()
        self.logger.info("Instagram configuration updated")

# Alias for backward compatibility
InstagramAPI = InstagramMessaging

# Example usage
if __name__ == "__main__":
    # Test with Unipile (primary)
    instagram = InstagramMessaging(use_unipile=True)

    # Check connection status
    status = instagram.get_connection_status()
    print(f"Connection status: {status}")

    # Check if configured
    if not instagram.is_configured():
        print("Instagram API not configured. Please update config.json with your credentials.")
    else:
        # Example: Get account info
        account_info = instagram.get_account_info()
        print(f"Account info: {account_info}")

        # Example: Send a test message via Unipile (replace with actual user ID)
        # result = instagram.send_message("USER_ID", "Hello from Instagram via Unipile!")
        # print(f"Message result: {result}")

        # Example: Test authentication
        auth_result = instagram.authenticate_account()
        print(f"Authentication result: {auth_result}")
